graph TD
    %% Main Entry Point
    A[main_with_vllm.py] --> B[asyncio.run(example_usage)]
    
    %% Application Initialization
    B --> C[MCPApp(name="mcp_basic_agent")]
    C --> D[app.run() context manager]
    D --> E[MCPApp.initialize()]
    E --> F[Context initialization]
    F --> G[Logger configuration<br/>level: debug, file transport]
    G --> H[Config loading from mcp_agent.config.yaml]
    
    %% Agent Creation and Setup
    D --> I[Agent(name="finder")]
    I --> J[Agent initialization with:<br/>- instruction<br/>- server_names: ["fetch", "filesystem"]]
    J --> K[Agent.__aenter__()]
    K --> L[Agent.initialize()]
    
    %% MCP Server Connection Management
    L --> M[MCPConnectionManager]
    M --> N[Launch fetch server:<br/>command: "uvx", args: ["mcp-server-fetch"]]
    M --> O[Launch filesystem server:<br/>command: "npx", args: ["-y", "@modelcontextprotocol/server-filesystem"]]
    
    %% Server Initialization Protocol
    N --> P[MCP Protocol Handshake<br/>fetch server]
    O --> Q[MCP Protocol Handshake<br/>filesystem server]
    P --> R[tools/list request<br/>fetch server]
    Q --> S[tools/list request<br/>filesystem server]
    
    %% Tool Discovery and Aggregation
    R --> T[Fetch tools:<br/>- fetch_fetch]
    S --> U[Filesystem tools:<br/>- filesystem_read_file<br/>- filesystem_write_file<br/>- filesystem_list_directory<br/>- etc.]
    T --> V[MCPAggregator.finder]
    U --> V
    V --> W[Agent.list_tools()]
    
    %% LLM Attachment and Configuration
    W --> X[finder_agent.attach_llm(VLLMAugmentedLLM)]
    X --> Y[VLLMAugmentedLLM initialization]
    Y --> Z[vLLM API configuration:<br/>api_base: "http://0.0.0.0:28701/v1"<br/>model: "Qwen/Qwen3-8B"<br/>api_key: "EMPTY"]
    
    %% Message Processing Flow
    Z --> AA[llm.generate_str(message)]
    AA --> BB[VLLMAugmentedLLM.generate()]
    BB --> CC[Prepare ChatCompletion request:<br/>- system message (agent instruction)<br/>- user message<br/>- available tools<br/>- enable_thinking: true]
    
    %% vLLM API Call
    CC --> DD[VLLMCompletionTasks.request_completion_task]
    DD --> EE[OpenAI client call to vLLM:<br/>POST /v1/chat/completions]
    EE --> FF[vLLM response with tool_calls:<br/>finish_reason: "tool_calls"<br/>reasoning_content included]
    
    %% Tool Execution Flow
    FF --> GG[Extract tool call:<br/>name: "filesystem_read_file"<br/>arguments: {"path": "mcp_agent.config.yaml"}]
    GG --> HH[Agent.call_tool()]
    HH --> II[MCPAggregator tool routing]
    II --> JJ[MCP tools/call request to filesystem server]
    JJ --> KK[Filesystem server reads file]
    KK --> LL[Return file contents as CallToolResult]
    
    %% Response Generation
    LL --> MM[Add tool result to conversation]
    MM --> NN[Second vLLM API call with tool result]
    NN --> OO[Final response generation]
    OO --> PP[Return formatted response to user]
    
    %% Cleanup and Termination
    PP --> QQ[Agent.__aexit__()]
    QQ --> RR[MCPConnectionManager cleanup]
    RR --> SS[Server connections terminated]
    SS --> TT[Application shutdown]
    
    %% Key Parameter Flows
    subgraph "Key Parameters & Configuration"
        direction TB
        U1[Settings:<br/>execution_engine: "asyncio"<br/>logger.level: "debug"<br/>vllm.default_model: "Qwen/Qwen3-8B"]
        U2[Agent Configuration:<br/>name: "finder"<br/>server_names: ["fetch", "filesystem"]<br/>instruction: "You are an agent..."]
        U3[vLLM Request Parameters:<br/>model: "Qwen/Qwen3-8B"<br/>max_tokens: 4096<br/>temperature: 0.7<br/>enable_thinking: true]
        U4[Tool Call Parameters:<br/>name: "filesystem_read_file"<br/>arguments: {"path": "mcp_agent.config.yaml"}]
    end
    
    %% Styling
    classDef entryPoint fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef appLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef agentLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef mcpLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef llmLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef toolLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef configBox fill:#f5f5f5,stroke:#424242,stroke-width:1px,stroke-dasharray: 5 5
    
    class A,B entryPoint
    class C,D,E,F,G,H appLayer
    class I,J,K,L,W,X,HH agentLayer
    class M,N,O,P,Q,R,S,T,U,V,II,JJ,KK,LL mcpLayer
    class Y,Z,AA,BB,CC,DD,EE,FF,NN,OO,PP llmLayer
    class GG,MM toolLayer
    class U1,U2,U3,U4 configBox
