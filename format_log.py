#!/usr/bin/env python3
import json
import sys
import os
from datetime import datetime
import argparse
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich import box

def format_timestamp(timestamp_str):
    """Format timestamp to a more readable format"""
    dt = datetime.fromisoformat(timestamp_str)
    return dt.strftime("%H:%M:%S.%f")[:-3]

def format_log_entry(entry):
    """Format a single log entry"""
    console = Console(width=120, highlight=False)
    
    # Parse the JSON entry
    try:
        log_entry = json.loads(entry)
    except json.JSONDecodeError:
        return f"Error parsing log entry: {entry}"
    
    # Extract basic information
    level = log_entry.get("level", "UNKNOWN")
    timestamp = format_timestamp(log_entry.get("timestamp", ""))
    namespace = log_entry.get("namespace", "")
    message = log_entry.get("message", "")
    
    # Create a table for the log entry
    table = Table(box=box.SIMPLE, show_header=False, padding=(0, 1))
    table.add_column("Field", style="bold cyan", width=12)
    table.add_column("Value", style="white")
    
    # Add the basic information
    table.add_row("LEVEL", Text(level, style=get_level_style(level)))
    table.add_row("TIME", timestamp)
    table.add_row("NAMESPACE", namespace)
    table.add_row("MESSAGE", message)
    
    # Add data if present
    if "data" in log_entry and "data" in log_entry["data"]:
        data = log_entry["data"]["data"]
        formatted_data = json.dumps(data, indent=2)
        table.add_row("DATA", formatted_data)
    
    # Create a panel with the table
    panel = Panel(
        table,
        title=f"[{timestamp}] {level}",
        border_style=get_level_style(level),
        expand=False
    )
    
    console.print(panel)
    return ""

def get_level_style(level):
    """Return a style based on the log level"""
    level_styles = {
        "DEBUG": "dim blue",
        "INFO": "green",
        "WARNING": "yellow",
        "ERROR": "red",
        "CRITICAL": "bold red"
    }
    return level_styles.get(level, "white")

def main():
    parser = argparse.ArgumentParser(description="Format JSONL log files for better readability")
    parser.add_argument("log_file", help="Path to the JSONL log file")
    parser.add_argument("--output", "-o", help="Output file (default: stdout)")
    parser.add_argument("--filter", "-f", help="Filter logs by namespace")
    parser.add_argument("--level", "-l", help="Filter logs by minimum level")
    
    args = parser.parse_args()
    
    # Check if the file exists
    if not os.path.exists(args.log_file):
        print(f"Error: File {args.log_file} does not exist")
        sys.exit(1)
    
    # Redirect output if specified
    if args.output:
        sys.stdout = open(args.output, "w")
    
    # Process the file
    with open(args.log_file, "r") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            
            # Apply filters if specified
            try:
                entry = json.loads(line)
                
                if args.filter and args.filter not in entry.get("namespace", ""):
                    continue
                
                if args.level:
                    levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
                    if levels.index(entry.get("level", "")) < levels.index(args.level):
                        continue
                
                format_log_entry(line)
            except json.JSONDecodeError:
                print(f"Error parsing log entry: {line}")

if __name__ == "__main__":
    main()
