#!/usr/bin/env python3
"""
Script to test OTLP endpoint connectivity for MCP Agent OpenTelemetry configuration.
"""

import requests
import json
import sys
from urllib.parse import urlparse


def test_otlp_endpoint(endpoint_url: str) -> bool:
    """
    Test if an OTLP endpoint is reachable and accepting traces.
    
    Args:
        endpoint_url: The OTLP endpoint URL (e.g., http://localhost:4318/v1/traces)
    
    Returns:
        True if endpoint is working, False otherwise
    """
    print(f"Testing OTLP endpoint: {endpoint_url}")
    
    # Parse URL to validate format
    try:
        parsed = urlparse(endpoint_url)
        if not parsed.scheme or not parsed.netloc:
            print("❌ Invalid URL format")
            return False
    except Exception as e:
        print(f"❌ URL parsing error: {e}")
        return False
    
    # Prepare minimal OTLP trace payload
    test_payload = {
        "resourceSpans": [
            {
                "resource": {
                    "attributes": [
                        {
                            "key": "service.name",
                            "value": {"stringValue": "test-service"}
                        }
                    ]
                },
                "scopeSpans": [
                    {
                        "scope": {
                            "name": "test-tracer"
                        },
                        "spans": [
                            {
                                "traceId": "12345678901234567890123456789012",
                                "spanId": "1234567890123456",
                                "name": "test-span",
                                "kind": 1,
                                "startTimeUnixNano": "1640995200000000000",
                                "endTimeUnixNano": "1640995201000000000",
                                "status": {"code": 1}
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "mcp-agent-otlp-test/1.0"
    }
    
    try:
        # Test basic connectivity first
        print("🔍 Testing basic connectivity...")
        response = requests.get(
            f"{parsed.scheme}://{parsed.netloc}/",
            timeout=5
        )
        print(f"   Base URL response: {response.status_code}")
        
        # Test OTLP endpoint
        print("🔍 Testing OTLP traces endpoint...")
        response = requests.post(
            endpoint_url,
            headers=headers,
            data=json.dumps(test_payload),
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ OTLP endpoint is working correctly!")
            print(f"   Response: {response.status_code} {response.reason}")
            return True
        elif response.status_code == 404:
            print("❌ OTLP endpoint not found (404)")
            print("   Check if the collector is running and endpoint path is correct")
            print("   Common endpoints:")
            print("   - Jaeger: http://localhost:4318/v1/traces")
            print("   - OTEL Collector: http://localhost:4318/v1/traces")
            return False
        else:
            print(f"⚠️  Unexpected response: {response.status_code} {response.reason}")
            print(f"   Response body: {response.text[:200]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - collector may not be running")
        print("   To start Jaeger locally:")
        print("   docker run -d --name jaeger -p 16686:16686 -p 4318:4318 jaegertracing/all-in-one:latest")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timed out - collector may be overloaded")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def main():
    """Main function to test OTLP endpoints."""
    if len(sys.argv) != 2:
        print("Usage: python test_otlp_endpoint.py <endpoint_url>")
        print("Example: python test_otlp_endpoint.py http://localhost:4318/v1/traces")
        sys.exit(1)
    
    endpoint_url = sys.argv[1]
    
    print("🚀 OTLP Endpoint Connectivity Test")
    print("=" * 50)
    
    success = test_otlp_endpoint(endpoint_url)
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Test completed successfully!")
        print("Your MCP Agent OTLP configuration should work with this endpoint.")
    else:
        print("❌ Test failed!")
        print("Please check your collector setup and endpoint configuration.")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
