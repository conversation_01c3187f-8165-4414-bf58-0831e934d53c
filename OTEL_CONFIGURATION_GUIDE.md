# OpenTelemetry Configuration Guide for MCP Agent

This guide explains how to properly configure OpenTelemetry (OTEL) tracing in your `mcp_agent.config.yaml` file.

## 1. Basic Configuration to Enable Tracing

### Minimum Required Settings
```yaml
otel:
  enabled: true
  exporters: ["console"]
  service_name: "my-mcp-service"
```

### Complete Configuration Example
```yaml
otel:
  enabled: true
  exporters: ["console", "file", "otlp"]
  service_name: "my-mcp-service"
  service_instance_id: "instance-1"
  service_version: "1.0.0"
  sample_rate: 1.0
  otlp_settings:
    endpoint: "http://localhost:4318/v1/traces"
  path_settings:
    path_pattern: "traces/mcp-agent-trace-{unique_id}.jsonl"
    unique_id: "timestamp"
    timestamp_format: "%Y%m%d_%H%M%S"
```

## 2. Exporter Configuration

### Available Exporters
- **console**: Outputs traces to console/terminal
- **file**: Saves traces to JSON files
- **otlp**: Sends traces to OTLP-compatible collectors (<PERSON><PERSON><PERSON>, etc.)

### Console Exporter
```yaml
otel:
  enabled: true
  exporters: ["console"]
  service_name: "my-service"
```

### File Exporter
```yaml
otel:
  enabled: true
  exporters: ["file"]
  service_name: "my-service"
  path_settings:
    path_pattern: "traces/my-service-{unique_id}.jsonl"
    unique_id: "timestamp"  # or "session_id"
    timestamp_format: "%Y%m%d_%H%M%S"
```

### OTLP Exporter (for Jaeger/other collectors)
```yaml
otel:
  enabled: true
  exporters: ["otlp"]
  service_name: "my-service"
  otlp_settings:
    endpoint: "http://localhost:4318/v1/traces"
```

## 3. Service Name Configuration

The service name is crucial for identifying your service in traces:

```yaml
otel:
  enabled: true
  exporters: ["console"]
  service_name: "my-microservice"           # Required: appears in trace UI
  service_instance_id: "pod-123"           # Optional: unique instance ID
  service_version: "v2.1.0"                # Optional: service version
```

## 4. Jaeger/Collector Configuration

### Local Jaeger Setup
1. Install Jaeger locally:
   ```bash
   docker run -d --name jaeger \
     -p 16686:16686 \
     -p 14268:14268 \
     -p 4317:4317 \
     -p 4318:4318 \
     jaegertracing/all-in-one:latest
   ```

2. Configure MCP Agent:
   ```yaml
   otel:
     enabled: true
     exporters: ["otlp"]
     service_name: "my-mcp-service"
     otlp_settings:
       endpoint: "http://localhost:4318/v1/traces"  # HTTP endpoint
       # For gRPC use: "http://localhost:4317"
   ```

### Remote Collector
```yaml
otel:
  enabled: true
  exporters: ["otlp"]
  service_name: "my-service"
  otlp_settings:
    endpoint: "https://your-collector.example.com:4318/v1/traces"
```

## 5. Additional Configuration Options

### Sampling Rate
Control what percentage of traces to collect:
```yaml
otel:
  enabled: true
  exporters: ["console"]
  service_name: "my-service"
  sample_rate: 0.1  # Sample 10% of traces (reduces overhead)
```

### Multiple Exporters
You can enable multiple exporters simultaneously:
```yaml
otel:
  enabled: true
  exporters: ["console", "file", "otlp"]  # All three enabled
  service_name: "my-service"
  otlp_settings:
    endpoint: "http://localhost:4318/v1/traces"
  path_settings:
    path_pattern: "traces/debug-{unique_id}.jsonl"
    unique_id: "session_id"
```

## 6. Beyond Configuration File

### Required Dependencies
The MCP Agent automatically handles OpenTelemetry instrumentation for:
- OpenAI API calls
- Anthropic API calls
- Internal MCP operations

### No Additional Code Required
Once configured, tracing works automatically. The system will:
- Create spans for LLM calls
- Track MCP server interactions
- Record workflow/activity execution
- Propagate trace context across operations

## 7. Testing OTLP Endpoint

### Method 1: Check Jaeger UI
1. Open Jaeger UI: http://localhost:16686
2. Run your MCP Agent
3. Check for traces in the UI under your service name

### Method 2: Test with curl
```bash
# Test if OTLP endpoint is reachable
curl -X POST http://localhost:4318/v1/traces \
  -H "Content-Type: application/json" \
  -d '{"resourceSpans":[]}'

# Should return 200 OK if endpoint is working
```

### Method 3: Check MCP Agent Logs
Look for these log messages:
- ✅ "OpenTelemetry configured successfully"
- ❌ "OTLP exporter is enabled but no OTLP settings endpoint is provided"
- ❌ "Failed to export span"

### Method 4: Verify with Console Exporter
Temporarily add console exporter to see traces in terminal:
```yaml
otel:
  enabled: true
  exporters: ["console", "otlp"]  # Add console for debugging
  service_name: "my-service"
  otlp_settings:
    endpoint: "http://localhost:4318/v1/traces"
```

## 8. Troubleshooting

### Common Issues
1. **No traces appearing**: Check `enabled: true` and valid exporters
2. **OTLP connection failed**: Verify endpoint URL and collector is running
3. **File traces not created**: Check file permissions and directory exists
4. **Service not appearing in Jaeger**: Verify service_name matches

### Debug Configuration
```yaml
otel:
  enabled: true
  exporters: ["console", "file"]  # Start with local exporters
  service_name: "debug-service"
  sample_rate: 1.0  # Capture all traces for debugging
```
