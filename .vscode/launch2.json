{"version": "0.2.0", "configurations": [{"name": "Python: Debug simple_text_generation.py", "type": "python", "request": "launch", "program": "${workspaceFolder}/examples/simple_text_generation.py", "pythonPath": "/usr/bin/python3", "env": {"LD_LIBRARY_PATH": "/opt/nvidia/nsight-compute/2023.3.1/host/linux-desktop-glibc_2_11_3-x64/Mesa:${env:LD_LIBRARY_PATH}"}, "args": ["--model", "/home/<USER>/llm/llama2/llama-2-7b-chat.Q4_K_M.gguf", "--framework", "nim"], "console": "integratedTerminal", "justMyCode": false}]}