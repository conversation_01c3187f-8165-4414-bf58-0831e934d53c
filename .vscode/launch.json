// {
//     "version": "0.2.0",
//     "configurations": [
//         {
//             "name": "Debug MCP Bridge",
//             "cwd": "${workspaceFolder}/python-sdk/MCP-Bridge",
//             "request": "launch",
//             "type": "debugpy",
//             // "program": "/root/miniconda3/bin/python",  
//             "python": "${command:python.interpreterPath}",  
//             "program": "${workspaceFolder}/python-sdk/MCP-Bridge/mcp_bridge/main.py",
//             // "args": [
//             //     "${workspaceFolder}/python-sdk/MCP-Bridge/mcp_bridge/main.py"
//             // ],
//             "env": {
//                 // "PYTHONPATH": "${workspaceFolder}/python-sdk/MCP-Bridge",
//                 "MCP_BRIDGE_CONFIG": "${workspaceFolder}/python-sdk/MCP-Bridge/mcp_bridge/config.yaml"
//                 // "PYTHONUNBUFFERED": "1"
//             },
//             "justMyCode": false,
//             "console": "integratedTerminal"
//         }
//     ]
// }
// {
//     "version": "0.2.0",
//     "configurations": [
//         {
//             "name": "Debug MCP Client with uv",
//             "type": "python",
//             "request": "launch",
//             "program": "${workspaceFolder}/python-sdk/examples/client/mcp-client-test/client_pip.py",
//             "args": [
//                 "${workspaceFolder}/python-sdk/examples/servers/simple-tool/mcp_simple_tool/server.py"
//             ],
//             "cwd": "${workspaceFolder}/python-sdk/examples/client/mcp-client-test",
//             "justMyCode": false,
//             "showReturnValue": true,
//             "console": "integratedTerminal",
//             "env": {
//                 "PYTHONPATH": "${workspaceFolder}/python-sdk/examples/client/mcp-client-test",
//                 "PYTHONASYNCIODEBUG": "1"
//             },
//             "stopOnEntry": false,
//             "redirectOutput": true
//         }
//     ]
// }
// {
//     "version": "0.2.0",
//     "configurations": [
//         {
//             "name": "Python: Debug test_client.py",
//             "type": "python",
//             "request": "launch",
//             "program": "${workspaceFolder}/python-sdk/examples/fastmcp/test_client.py",
//             "pythonPath": "/usr/bin/python3"
//             // "env": {
//             //     "LD_LIBRARY_PATH": "/opt/nvidia/nsight-compute/2023.3.1/host/linux-desktop-glibc_2_11_3-x64/Mesa:${env:LD_LIBRARY_PATH}"
//             // },
//             // "args": [
//             //     "--action", "search",
//             //     "--model", "meta-llama/Llama-3.2-11B-Vision-Instruct",
//             //     "--framework", "qgenai",
//             //     "--query", "c",
//             //     "--store_path", "/merge/vectorstore"
//             // ],
//             // "args": [
//             //     "--action", "create",
//             //     "--model", "meta-llama/Llama-3.2-11B-Vision-Instruct",
//             //     "--framework", "qgenai",
//             //     "--nim_host", "http://0.0.0.0:8000",
//             //     "--file", "/merge/genai-devkit/testfile/TestDataSet_Document_Reasoning.pdf"
//             // ],
//             "console": "integratedTerminal",
//             "justMyCode": false
//         }
//     ]
// }


// {
//     "version": "0.2.0",
//     "configurations": [
//         {
//             "name": "Python: Debug simple_rag.py",
//             "type": "python",
//             "request": "launch",
//             "program": "${workspaceFolder}/genai-devkit/examples/simple_rag.py",
//             "pythonPath": "/usr/bin/python3",
//             "env": {
//                 "LD_LIBRARY_PATH": "/opt/nvidia/nsight-compute/2023.3.1/host/linux-desktop-glibc_2_11_3-x64/Mesa:${env:LD_LIBRARY_PATH}"
//             },
//             // "args": [
//             //     "--action", "search",
//             //     "--model", "meta-llama/Llama-3.2-11B-Vision-Instruct",
//             //     "--framework", "qgenai",
//             //     "--query", "c",
//             //     "--store_path", "/merge/vectorstore"
//             // ],
//             "args": [
//                 "--action", "create",
//                 "--model", "meta-llama/Llama-3.2-11B-Vision-Instruct",
//                 "--framework", "qgenai",
//                 "--file", "/merge/genai-devkit/testfile/TestDataSet_Document_Reasoning.pdf"
//             ],
//             "console": "integratedTerminal",
//             "justMyCode": false
//         }
//     ]
// }

{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Flask",
            "type": "python",
            "request": "launch",
            "module": "flask",
            "env": {
                "FLASK_APP": "${workspaceFolder}/genai-devkit/src/genai_api",  // Adjust the path to your main Flask app file
                "FLASK_ENV": "development",
                "FLASK_DEBUG": "1",
                "LD_LIBRARY_PATH": "/opt/nvidia/nsight-compute/2023.3.1/host/linux-desktop-glibc_2_11_3-x64/Mesa:${env:LD_LIBRARY_PATH}",
                "QCT_LOG_CONFIG": "/merge/genai-devkit/examples/config/logging_debug.yaml",
                "pythonPath": "/usr/bin/python3"
            },
            "args": [
                "run",
                "--no-debugger",
                "--no-reload"
            ],
            "jinja": true,
            "console": "integratedTerminal",
            "justMyCode": false
        }
    ]
}