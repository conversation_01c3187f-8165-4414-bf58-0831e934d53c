╭────────────────── [14:34:23.126] INFO ──────────────────╮
│                                                         │
│   LEVEL          INFO                                   │
│   TIME           14:34:23.126                           │
│   NAMESPACE      mcp_agent.context                      │
│   MESSAGE        Configuring logger with level: debug   │
│                                                         │
╰─────────────────────────────────────────────────────────╯
╭─────────────────────────── [14:34:23.128] INFO ───────────────────────────╮
│                                                                           │
│   LEVEL          INFO                                                     │
│   TIME           14:34:23.128                                             │
│   NAMESPACE      mcp_agent.mcp_basic_agent                                │
│   MESSAGE        MCPAgent initialized                                     │
│   DATA           {                                                        │
│                    "progress_action": "Running",                          │
│                    "target": "mcp_basic_agent",                           │
│                    "agent_name": "mcp_application_loop",                  │
│                    "session_id": "eb4aa0dd-474a-4f8a-bf46-7c1339c7e5e7"   │
│                  }                                                        │
│                                                                           │
╰───────────────────────────────────────────────────────────────────────────╯
╭───────────────────────────── [14:34:23.128] INFO ──────────────────────────────╮
│                                                                                │
│   LEVEL          INFO                                                          │
│   TIME           14:34:23.128                                                  │
│   NAMESPACE      mcp_agent.mcp_basic_agent                                     │
│   MESSAGE        Current config:                                               │
│   DATA           {                                                             │
│                    "mcp": {                                                    │
│                      "servers": {                                              │
│                        "fetch": {                                              │
│                          "name": null,                                         │
│                          "description": null,                                  │
│                          "transport": "stdio",                                 │
│                          "command": "uvx",                                     │
│                          "args": [                                             │
│                            "mcp-server-fetch"                                  │
│                          ],                                                    │
│                          "read_timeout_seconds": null,                         │
│                          "url": null,                                          │
│                          "auth": null,                                         │
│                          "headers": null,                                      │
│                          "roots": null,                                        │
│                          "env": null                                           │
│                        },                                                      │
│                        "filesystem": {                                         │
│                          "name": null,                                         │
│                          "description": null,                                  │
│                          "transport": "stdio",                                 │
│                          "command": "npx",                                     │
│                          "args": [                                             │
│                            "-y",                                               │
│                            "@modelcontextprotocol/server-filesystem"           │
│                          ],                                                    │
│                          "read_timeout_seconds": null,                         │
│                          "url": null,                                          │
│                          "auth": null,                                         │
│                          "headers": null,                                      │
│                          "roots": null,                                        │
│                          "env": null                                           │
│                        }                                                       │
│                      }                                                         │
│                    },                                                          │
│                    "execution_engine": "asyncio",                              │
│                    "temporal": null,                                           │
│                    "anthropic": {                                              │
│                      "api_key": "anthropic_....."                              │
│                    },                                                          │
│                    "bedrock": null,                                            │
│                    "cohere": null,                                             │
│                    "openai": {                                                 │
│                      "api_key": "sk-EHTmbiL.....",                             │
│                      "reasoning_effort": "medium",                             │
│                      "base_url": null,                                         │
│                      "http_client": null,                                      │
│                      "default_model": "Qwen/Qwen3-8B"                          │
│                    },                                                          │
│                    "azure": null,                                              │
│                    "google": null,                                             │
│                    "vllm": null,                                               │
│                    "otel": {                                                   │
│                      "enabled": true,                                          │
│                      "service_name": "mcp-agent",                              │
│                      "service_instance_id": null,                              │
│                      "service_version": null,                                  │
│                      "otlp_endpoint": null,                                    │
│                      "console_debug": false,                                   │
│                      "sample_rate": 1.0                                        │
│                    },                                                          │
│                    "logger": {                                                 │
│                      "type": "console",                                        │
│                      "transports": [                                           │
│                        "console",                                              │
│                        "file"                                                  │
│                      ],                                                        │
│                      "level": "debug",                                         │
│                      "progress_display": "True",                               │
│                      "path": "mcp-agent.jsonl",                                │
│                      "path_settings": {                                        │
│                        "path_pattern": "logs/mcp-agent-{unique_id}.jsonl",     │
│                        "unique_id": "timestamp",                               │
│                        "timestamp_format": "%Y%m%d_%H%M%S"                     │
│                      },                                                        │
│                      "batch_size": 100,                                        │
│                      "flush_interval": 2.0,                                    │
│                      "max_queue_size": 2048,                                   │
│                      "http_endpoint": null,                                    │
│                      "http_headers": null,                                     │
│                      "http_timeout": 5.0                                       │
│                    },                                                          │
│                    "usage_telemetry": {                                        │
│                      "enabled": "True",                                        │
│                      "enable_detailed_telemetry": "False"                      │
│                    },                                                          │
│                    "$schema": "../../../schema/mcp-agent.config.schema.json"   │
│                  }                                                             │
│                                                                                │
╰────────────────────────────────────────────────────────────────────────────────╯
╭───────────────────────── [14:34:23.143] INFO ──────────────────────────╮
│                                                                        │
│   LEVEL          INFO                                                  │
│   TIME           14:34:23.143                                          │
│   NAMESPACE      mcp_agent.mcp.mcp_connection_manager                  │
│   MESSAGE        fetch: Up and running with a persistent connection!   │
│                                                                        │
╰────────────────────────────────────────────────────────────────────────╯
╭──────────────────────────── [14:34:23.146] INFO ────────────────────────────╮
│                                                                             │
│   LEVEL          INFO                                                       │
│   TIME           14:34:23.146                                               │
│   NAMESPACE      mcp_agent.mcp.mcp_connection_manager                       │
│   MESSAGE        filesystem: Up and running with a persistent connection!   │
│                                                                             │
╰─────────────────────────────────────────────────────────────────────────────╯
╭───────────────────────── [14:34:24.444] INFO ─────────────────────────╮
│                                                                       │
│   LEVEL          INFO                                                 │
│   TIME           14:34:24.444                                         │
│   NAMESPACE      mcp_agent.mcp_basic_agent                            │
│   MESSAGE        finder: Connected to server, calling list_tools...   │
│                                                                       │
╰───────────────────────────────────────────────────────────────────────╯
╭──────────────────────────────────────────────── [14:34:24.451] INFO ─────────────────────────────────────────────────╮
│                                                                                                                      │
│   LEVEL          INFO                                                                                                │
│   TIME           14:34:24.451                                                                                        │
│   NAMESPACE      mcp_agent.mcp_basic_agent                                                                           │
│   MESSAGE        Tools available:                                                                                    │
│   DATA           {                                                                                                   │
│                    "meta": null,                                                                                     │
│                    "nextCursor": null,                                                                               │
│                    "tools": [                                                                                        │
│                      {                                                                                               │
│                        "name": "fetch_fetch",                                                                        │
│                        "description": "Fetches a URL from the internet and optionally extracts its contents as       │
│                  markdown.\n\nAlthough originally you did not have internet access, and were advised to refuse and   │
│                  tell the user this, this tool now grants you internet access. Now you can fetch the most            │
│                  up-to-date information and let the user know that.",                                                │
│                        "inputSchema": {                                                                              │
│                          "description": "Parameters for fetching a URL.",                                            │
│                          "properties": {                                                                             │
│                            "url": {                                                                                  │
│                              "description": "URL to fetch",                                                          │
│                              "format": "uri",                                                                        │
│                              "minLength": 1,                                                                         │
│                              "title": "Url",                                                                         │
│                              "type": "string"                                                                        │
│                            },                                                                                        │
│                            "max_length": {                                                                           │
│                              "default": 5000,                                                                        │
│                              "description": "Maximum number of characters to return.",                               │
│                              "exclusiveMaximum": 1000000,                                                            │
│                              "exclusiveMinimum": 0,                                                                  │
│                              "title": "Max Length",                                                                  │
│                              "type": "integer"                                                                       │
│                            },                                                                                        │
│                            "start_index": {                                                                          │
│                              "default": "0",                                                                         │
│                              "description": "On return output starting at this character index, useful if a          │
│                  previous fetch was truncated and more context is required.",                                        │
│                              "minimum": "0",                                                                         │
│                              "title": "Start Index",                                                                 │
│                              "type": "integer"                                                                       │
│                            },                                                                                        │
│                            "raw": {                                                                                  │
│                              "default": false,                                                                       │
│                              "description": "Get the actual HTML content of the requested page, without              │
│                  simplification.",                                                                                   │
│                              "title": "Raw",                                                                         │
│                              "type": "boolean"                                                                       │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "url"                                                                                     │
│                          ],                                                                                          │
│                          "title": "Fetch",                                                                           │
│                          "type": "object"                                                                            │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "filesystem_read_file",                                                               │
│                        "description": "Read the complete contents of a file from the file system. Handles various    │
│                  text encodings and provides detailed error messages if the file cannot be read. Use this tool       │
│                  when you need to examine the contents of a single file. Only works within allowed directories.",    │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {                                                                             │
│                            "path": {                                                                                 │
│                              "type": "string"                                                                        │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "path"                                                                                    │
│                          ],                                                                                          │
│                          "additionalProperties": "False",                                                            │
│                          "$schema": "http://json-schema.org/draft-07/schema#"                                        │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "filesystem_read_multiple_files",                                                     │
│                        "description": "Read the contents of multiple files simultaneously. This is more efficient    │
│                  than reading files one by one when you need to analyze or compare multiple files. Each file's       │
│                  content is returned with its path as a reference. Failed reads for individual files won't stop      │
│                  the entire operation. Only works within allowed directories.",                                      │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {                                                                             │
│                            "paths": {                                                                                │
│                              "type": "array",                                                                        │
│                              "items": {                                                                              │
│                                "type": "string"                                                                      │
│                              }                                                                                       │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "paths"                                                                                   │
│                          ],                                                                                          │
│                          "additionalProperties": "False",                                                            │
│                          "$schema": "http://json-schema.org/draft-07/schema#"                                        │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "filesystem_write_file",                                                              │
│                        "description": "Create a new file or completely overwrite an existing file with new           │
│                  content. Use with caution as it will overwrite existing files without warning. Handles text         │
│                  content with proper encoding. Only works within allowed directories.",                              │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {                                                                             │
│                            "path": {                                                                                 │
│                              "type": "string"                                                                        │
│                            },                                                                                        │
│                            "content": {                                                                              │
│                              "type": "string"                                                                        │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "path",                                                                                   │
│                            "content"                                                                                 │
│                          ],                                                                                          │
│                          "additionalProperties": "False",                                                            │
│                          "$schema": "http://json-schema.org/draft-07/schema#"                                        │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "filesystem_edit_file",                                                               │
│                        "description": "Make line-based edits to a text file. Each edit replaces exact line           │
│                  sequences with new content. Returns a git-style diff showing the changes made. Only works within    │
│                  allowed directories.",                                                                              │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {                                                                             │
│                            "path": {                                                                                 │
│                              "type": "string"                                                                        │
│                            },                                                                                        │
│                            "edits": {                                                                                │
│                              "type": "array",                                                                        │
│                              "items": {                                                                              │
│                                "type": "object",                                                                     │
│                                "properties": {                                                                       │
│                                  "oldText": {                                                                        │
│                                    "type": "string",                                                                 │
│                                    "description": "Text to search for - must match exactly"                          │
│                                  },                                                                                  │
│                                  "newText": {                                                                        │
│                                    "type": "string",                                                                 │
│                                    "description": "Text to replace with"                                             │
│                                  }                                                                                   │
│                                },                                                                                    │
│                                "required": [                                                                         │
│                                  "oldText",                                                                          │
│                                  "newText"                                                                           │
│                                ],                                                                                    │
│                                "additionalProperties": "False"                                                       │
│                              }                                                                                       │
│                            },                                                                                        │
│                            "dryRun": {                                                                               │
│                              "type": "boolean",                                                                      │
│                              "default": "False",                                                                     │
│                              "description": "Preview changes using git-style diff format"                            │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "path",                                                                                   │
│                            "edits"                                                                                   │
│                          ],                                                                                          │
│                          "additionalProperties": "False",                                                            │
│                          "$schema": "http://json-schema.org/draft-07/schema#"                                        │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "filesystem_create_directory",                                                        │
│                        "description": "Create a new directory or ensure a directory exists. Can create multiple      │
│                  nested directories in one operation. If the directory already exists, this operation will succeed   │
│                  silently. Perfect for setting up directory structures for projects or ensuring required paths       │
│                  exist. Only works within allowed directories.",                                                     │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {                                                                             │
│                            "path": {                                                                                 │
│                              "type": "string"                                                                        │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "path"                                                                                    │
│                          ],                                                                                          │
│                          "additionalProperties": "False",                                                            │
│                          "$schema": "http://json-schema.org/draft-07/schema#"                                        │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "filesystem_list_directory",                                                          │
│                        "description": "Get a detailed listing of all files and directories in a specified path.      │
│                  Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This      │
│                  tool is essential for understanding directory structure and finding specific files within a         │
│                  directory. Only works within allowed directories.",                                                 │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {                                                                             │
│                            "path": {                                                                                 │
│                              "type": "string"                                                                        │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "path"                                                                                    │
│                          ],                                                                                          │
│                          "additionalProperties": "False",                                                            │
│                          "$schema": "http://json-schema.org/draft-07/schema#"                                        │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "filesystem_directory_tree",                                                          │
│                        "description": "Get a recursive tree view of files and directories as a JSON structure.       │
│                  Each entry includes 'name', 'type' (file/directory), and 'children' for directories. Files have     │
│                  no children array, while directories always have a children array (which may be empty). The         │
│                  output is formatted with 2-space indentation for readability. Only works within allowed             │
│                  directories.",                                                                                      │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {                                                                             │
│                            "path": {                                                                                 │
│                              "type": "string"                                                                        │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "path"                                                                                    │
│                          ],                                                                                          │
│                          "additionalProperties": "False",                                                            │
│                          "$schema": "http://json-schema.org/draft-07/schema#"                                        │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "filesystem_move_file",                                                               │
│                        "description": "Move or rename files and directories. Can move files between directories      │
│                  and rename them in a single operation. If the destination exists, the operation will fail. Works    │
│                  across different directories and can be used for simple renaming within the same directory. Both    │
│                  source and destination must be within allowed directories.",                                        │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {                                                                             │
│                            "source": {                                                                               │
│                              "type": "string"                                                                        │
│                            },                                                                                        │
│                            "destination": {                                                                          │
│                              "type": "string"                                                                        │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "source",                                                                                 │
│                            "destination"                                                                             │
│                          ],                                                                                          │
│                          "additionalProperties": "False",                                                            │
│                          "$schema": "http://json-schema.org/draft-07/schema#"                                        │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "filesystem_search_files",                                                            │
│                        "description": "Recursively search for files and directories matching a pattern. Searches     │
│                  through all subdirectories from the starting path. The search is case-insensitive and matches       │
│                  partial names. Returns full paths to all matching items. Great for finding files when you don't     │
│                  know their exact location. Only searches within allowed directories.",                              │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {                                                                             │
│                            "path": {                                                                                 │
│                              "type": "string"                                                                        │
│                            },                                                                                        │
│                            "pattern": {                                                                              │
│                              "type": "string"                                                                        │
│                            },                                                                                        │
│                            "excludePatterns": {                                                                      │
│                              "type": "array",                                                                        │
│                              "items": {                                                                              │
│                                "type": "string"                                                                      │
│                              },                                                                                      │
│                              "default": []                                                                           │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "path",                                                                                   │
│                            "pattern"                                                                                 │
│                          ],                                                                                          │
│                          "additionalProperties": "False",                                                            │
│                          "$schema": "http://json-schema.org/draft-07/schema#"                                        │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "filesystem_get_file_info",                                                           │
│                        "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive   │
│                  information including size, creation time, last modified time, permissions, and type. This tool     │
│                  is perfect for understanding file characteristics without reading the actual content. Only works    │
│                  within allowed directories.",                                                                       │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {                                                                             │
│                            "path": {                                                                                 │
│                              "type": "string"                                                                        │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "path"                                                                                    │
│                          ],                                                                                          │
│                          "additionalProperties": "False",                                                            │
│                          "$schema": "http://json-schema.org/draft-07/schema#"                                        │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "filesystem_list_allowed_directories",                                                │
│                        "description": "Returns the list of directories that this server is allowed to access. Use    │
│                  this to understand which directories are available before trying to access files.",                 │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {},                                                                           │
│                          "required": []                                                                              │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      },                                                                                              │
│                      {                                                                                               │
│                        "name": "__human_input__",                                                                    │
│                        "description": "\n        Request input from a human user. Pauses the workflow until input    │
│                  is received.\n\n        Args:\n            request: The human input request\n\n        Returns:\n   │
│                  The input provided by the human\n\n        Raises:\n            TimeoutError: If the timeout is     │
│                  exceeded\n        ",                                                                                │
│                        "inputSchema": {                                                                              │
│                          "type": "object",                                                                           │
│                          "properties": {                                                                             │
│                            "request": {                                                                              │
│                              "description": "Represents a request for human input.",                                 │
│                              "properties": {                                                                         │
│                                "prompt": {                                                                           │
│                                  "title": "Prompt",                                                                  │
│                                  "type": "string"                                                                    │
│                                },                                                                                    │
│                                "description": {                                                                      │
│                                  "anyOf": [                                                                          │
│                                    {                                                                                 │
│                                      "type": "string"                                                                │
│                                    },                                                                                │
│                                    {                                                                                 │
│                                      "type": "null"                                                                  │
│                                    }                                                                                 │
│                                  ],                                                                                  │
│                                  "default": null,                                                                    │
│                                  "title": "Description"                                                              │
│                                },                                                                                    │
│                                "request_id": {                                                                       │
│                                  "anyOf": [                                                                          │
│                                    {                                                                                 │
│                                      "type": "string"                                                                │
│                                    },                                                                                │
│                                    {                                                                                 │
│                                      "type": "null"                                                                  │
│                                    }                                                                                 │
│                                  ],                                                                                  │
│                                  "default": null,                                                                    │
│                                  "title": "Request Id"                                                               │
│                                },                                                                                    │
│                                "workflow_id": {                                                                      │
│                                  "anyOf": [                                                                          │
│                                    {                                                                                 │
│                                      "type": "string"                                                                │
│                                    },                                                                                │
│                                    {                                                                                 │
│                                      "type": "null"                                                                  │
│                                    }                                                                                 │
│                                  ],                                                                                  │
│                                  "default": null,                                                                    │
│                                  "title": "Workflow Id"                                                              │
│                                },                                                                                    │
│                                "timeout_seconds": {                                                                  │
│                                  "anyOf": [                                                                          │
│                                    {                                                                                 │
│                                      "type": "integer"                                                               │
│                                    },                                                                                │
│                                    {                                                                                 │
│                                      "type": "null"                                                                  │
│                                    }                                                                                 │
│                                  ],                                                                                  │
│                                  "default": null,                                                                    │
│                                  "title": "Timeout Seconds"                                                          │
│                                },                                                                                    │
│                                "metadata": {                                                                         │
│                                  "anyOf": [                                                                          │
│                                    {                                                                                 │
│                                      "type": "object"                                                                │
│                                    },                                                                                │
│                                    {                                                                                 │
│                                      "type": "null"                                                                  │
│                                    }                                                                                 │
│                                  ],                                                                                  │
│                                  "default": null,                                                                    │
│                                  "title": "Metadata"                                                                 │
│                                }                                                                                     │
│                              },                                                                                      │
│                              "required": [                                                                           │
│                                "prompt"                                                                              │
│                              ],                                                                                      │
│                              "title": "HumanInputRequest",                                                           │
│                              "type": "object"                                                                        │
│                            }                                                                                         │
│                          },                                                                                          │
│                          "required": [                                                                               │
│                            "request"                                                                                 │
│                          ]                                                                                           │
│                        },                                                                                            │
│                        "annotations": null                                                                           │
│                      }                                                                                               │
│                    ]                                                                                                 │
│                  }                                                                                                   │
│                                                                                                                      │
╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯
╭──────────────────────── [14:34:24.451] INFO ─────────────────────────╮
│                                                                      │
│   LEVEL          INFO                                                │
│   TIME           14:34:24.451                                        │
│   NAMESPACE      mcp_agent.workflows.llm.augmented_llm_vllm.finder   │
│   MESSAGE        Using vLLM API at http://0.0.0.0:28701/v1           │
│                                                                      │
╰──────────────────────────────────────────────────────────────────────╯
╭────────────────── [14:34:29.865] INFO ──────────────────╮
│                                                         │
│   LEVEL          INFO                                   │
│   TIME           14:34:29.865                           │
│   NAMESPACE      mcp_agent.mcp.mcp_aggregator.finder    │
│   MESSAGE        Requesting tool call                   │
│   DATA           {                                      │
│                    "progress_action": "Calling Tool",   │
│                    "tool_name": "read_file",            │
│                    "server_name": "filesystem",         │
│                    "agent_name": "finder"               │
│                  }                                      │
│                                                         │
╰─────────────────────────────────────────────────────────╯
╭──────────────────────────────────────────────── [14:34:35.986] INFO ─────────────────────────────────────────────────╮
│                                                                                                                      │
│   LEVEL          INFO                                                                                                │
│   TIME           14:34:35.986                                                                                        │
│   NAMESPACE      mcp_agent.mcp_basic_agent                                                                           │
│   MESSAGE        mcp_agent.config.yaml contents:                                                                     │
│                                                                                                                      │
│                                                                                                                      │
│                                                                                                                      │
│                                                                                                                      │
│                  Here is the contents of `mcp_agent.config.yaml` verbatim:                                           │
│                                                                                                                      │
│                  ```yaml                                                                                             │
│                  $schema: ../../../schema/mcp-agent.config.schema.json                                               │
│                                                                                                                      │
│                  execution_engine: asyncio                                                                           │
│                  logger:                                                                                             │
│                    transports:                                                                                       │
│                    level: debug                                                                                      │
│                    progress_display: true                                                                            │
│                    path_settings:                                                                                    │
│                      path_pattern: "logs/mcp-agent-{unique_id}.jsonl"                                                │
│                      unique_id: "timestamp" # Options: "timestamp" or "session_id"                                   │
│                      timestamp_format: "%Y%m%d_%H%M%S"                                                               │
│                                                                                                                      │
│                  mcp:                                                                                                │
│                    servers:                                                                                          │
│                      fetch:                                                                                          │
│                        command: "uvx"                                                                                │
│                        args: ["mcp-server-fetch"]                                                                    │
│                      filesystem:                                                                                     │
│                        command: "npx"                                                                                │
│                        args: ["-y", "@modelcontextprotocol/server-filesystem"]                                       │
│                                                                                                                      │
│                  openai:                                                                                             │
│                    # Secrets (API keys, etc.) are stored in an mcp_agent.secrets.yaml file which can be gitignored   │
│                    #  default_model: "o3-mini"                                                                       │
│                    default_model: "Qwen/Qwen3-8B"                                                                    │
│                  ```                                                                                                 │
│                                                                                                                      │
│                  The file contains configuration settings for the MCP agent, including logging parameters, server    │
│                  configurations, and OpenAI model specifications.                                                    │
│                                                                                                                      │
╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯
╭────────────────────────────────── [14:34:35.986] INFO ──────────────────────────────────╮
│                                                                                         │
│   LEVEL          INFO                                                                   │
│   TIME           14:34:35.986                                                           │
│   NAMESPACE      mcp_agent.mcp.mcp_aggregator.finder                                    │
│   MESSAGE        Last aggregator closing, shutting down all persistent connections...   │
│                                                                                         │
╰─────────────────────────────────────────────────────────────────────────────────────────╯
╭───────────────────────── [14:34:35.986] INFO ─────────────────────────╮
│                                                                       │
│   LEVEL          INFO                                                 │
│   TIME           14:34:35.986                                         │
│   NAMESPACE      mcp_agent.mcp.mcp_connection_manager                 │
│   MESSAGE        Disconnecting all persistent server connections...   │
│                                                                       │
╰───────────────────────────────────────────────────────────────────────╯
╭────────────────── [14:34:36.009] INFO ──────────────────╮
│                                                         │
│   LEVEL          INFO                                   │
│   TIME           14:34:36.009                           │
│   NAMESPACE      mcp_agent.mcp.mcp_connection_manager   │
│   MESSAGE        fetch: Requesting shutdown...          │
│                                                         │
╰─────────────────────────────────────────────────────────╯
╭────────────────── [14:34:36.009] INFO ──────────────────╮
│                                                         │
│   LEVEL          INFO                                   │
│   TIME           14:34:36.009                           │
│   NAMESPACE      mcp_agent.mcp.mcp_connection_manager   │
│   MESSAGE        filesystem: Requesting shutdown...     │
│                                                         │
╰─────────────────────────────────────────────────────────╯
╭──────────────────────────── [14:34:36.009] INFO ─────────────────────────────╮
│                                                                              │
│   LEVEL          INFO                                                        │
│   TIME           14:34:36.009                                                │
│   NAMESPACE      mcp_agent.mcp.mcp_connection_manager                        │
│   MESSAGE        All persistent server connections signaled to disconnect.   │
│                                                                              │
╰──────────────────────────────────────────────────────────────────────────────╯
╭───────────────────────── [14:34:36.017] INFO ─────────────────────────╮
│                                                                       │
│   LEVEL          INFO                                                 │
│   TIME           14:34:36.017                                         │
│   NAMESPACE      mcp_agent.mcp.mcp_connection_manager                 │
│   MESSAGE        Disconnecting all persistent server connections...   │
│                                                                       │
╰───────────────────────────────────────────────────────────────────────╯
╭─────────────────────────────── [14:34:36.518] INFO ────────────────────────────────╮
│                                                                                    │
│   LEVEL          INFO                                                              │
│   TIME           14:34:36.518                                                      │
│   NAMESPACE      mcp_agent.mcp.mcp_aggregator.finder                               │
│   MESSAGE        Connection manager successfully closed and removed from context   │
│                                                                                    │
╰────────────────────────────────────────────────────────────────────────────────────╯
╭─────────────────── [14:34:36.518] INFO ───────────────────╮
│                                                           │
│   LEVEL          INFO                                     │
│   TIME           14:34:36.518                             │
│   NAMESPACE      mcp_agent.mcp_basic_agent                │
│   MESSAGE        MCPAgent cleanup                         │
│   DATA           {                                        │
│                    "progress_action": "Finished",         │
│                    "target": "mcp_basic_agent",           │
│                    "agent_name": "mcp_application_loop"   │
│                  }                                        │
│                                                           │
╰───────────────────────────────────────────────────────────╯
