#!/usr/bin/env python3
"""
Test script to verify that <PERSON>wen reasoning content logging works in both asyncio and temporal modes.
"""

import asyncio
import sys
import os
from unittest.mock import Mock, patch

# Add the mcp-agent source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "mcp-agent", "src"))

from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMCompletionTasks, VLLMRequestCompletionRequest, VLLMAugmentedLLM
from mcp_agent.config import VLLMSettings
from openai.types.chat import ChatCompletion, ChatCompletionMessage
from openai.types.chat.chat_completion import Choice
from openai.types.completion_usage import CompletionUsage


def create_mock_response_with_reasoning_field():
    """Create a mock vLLM response with reasoning_content field."""
    
    # Create a message with reasoning_content field (vLLM extension)
    message = ChatCompletionMessage(
        role="assistant",
        content="The answer is 12."
    )
    
    # Add reasoning_content as a custom field
    message.reasoning_content = "Let me think step by step. The user is asking for the square root of 144. I know that 12 × 12 = 144, so the answer is 12."
    
    choice = Choice(
        index=0,
        message=message,
        finish_reason="stop"
    )
    
    response = ChatCompletion(
        id="test-completion",
        object="chat.completion",
        created=1234567890,
        model="Qwen/Qwen3-8B",
        choices=[choice],
        usage=CompletionUsage(
            prompt_tokens=10,
            completion_tokens=50,
            total_tokens=60
        )
    )
    
    return response


def create_mock_response_with_thinking_tags():
    """Create a mock vLLM response with thinking tags in content."""
    
    content_with_reasoning = """<think>
Let me think about this step by step.

The user is asking for the square root of 144. I need to find what number, when multiplied by itself, equals 144.

Let me consider some perfect squares:
- 10² = 100 (too small)
- 11² = 121 (still too small)  
- 12² = 144 (this is it!)

So the square root of 144 is 12.
</think>

The square root of 144 is 12."""

    message = ChatCompletionMessage(
        role="assistant",
        content=content_with_reasoning
    )
    
    choice = Choice(
        index=0,
        message=message,
        finish_reason="stop"
    )
    
    response = ChatCompletion(
        id="test-completion-tags",
        object="chat.completion",
        created=1234567890,
        model="Qwen/Qwen3-8B",
        choices=[choice],
        usage=CompletionUsage(
            prompt_tokens=10,
            completion_tokens=50,
            total_tokens=60
        )
    )
    
    return response


def test_reasoning_extraction_methods():
    """Test that both reasoning extraction methods work."""
    print("Testing reasoning content extraction methods...")
    
    # Create a mock logger
    mock_logger = Mock()
    
    # Test 1: reasoning_content field
    print("  Testing reasoning_content field extraction...")
    response_with_field = create_mock_response_with_reasoning_field()
    VLLMCompletionTasks._extract_and_log_reasoning_content(response_with_field, mock_logger)
    
    # Verify reasoning_content was logged
    reasoning_field_logged = False
    for call in mock_logger.info.call_args_list:
        args, kwargs = call
        if "Qwen Reasoning Content" in args[0] and "data" in kwargs:
            reasoning_field_logged = True
            reasoning_text = kwargs["data"]["reasoning"]
            assert "step by step" in reasoning_text
            print(f"    ✓ Reasoning field captured: {reasoning_text[:50]}...")
            break
    
    assert reasoning_field_logged, "Reasoning content field should have been logged"
    
    # Test 2: thinking tags in content
    print("  Testing thinking tags extraction...")
    mock_logger.reset_mock()
    response_with_tags = create_mock_response_with_thinking_tags()
    VLLMCompletionTasks._extract_and_log_reasoning_content(response_with_tags, mock_logger)
    
    # Verify thinking tags were logged
    thinking_tags_logged = False
    for call in mock_logger.info.call_args_list:
        args, kwargs = call
        if "extracted from content" in args[0] and "data" in kwargs:
            thinking_tags_logged = True
            reasoning_text = kwargs["data"]["reasoning"]
            assert "Let me think about this step by step" in reasoning_text
            print(f"    ✓ Thinking tags captured: {reasoning_text[:50]}...")
            break
    
    assert thinking_tags_logged, "Thinking tags should have been logged"
    
    print("✓ Reasoning extraction methods work correctly")


def test_asyncio_mode_integration():
    """Test that asyncio mode calls the reasoning extraction."""
    print("Testing asyncio mode integration...")
    
    # Mock the VLLMAugmentedLLM instance
    
    # Create a mock instance
    mock_llm = Mock(spec=VLLMAugmentedLLM)
    mock_llm.logger = Mock()
    
    # Test the asyncio reasoning extraction method
    response = create_mock_response_with_reasoning_field()
    
    # Call the method directly
    VLLMAugmentedLLM._extract_and_log_reasoning_content_asyncio(mock_llm, response)
    
    # Verify it was called
    assert mock_llm.logger.info.called, "Asyncio reasoning extraction should log reasoning content"
    
    # Check the log call
    reasoning_logged = False
    for call in mock_llm.logger.info.call_args_list:
        args, kwargs = call
        if "asyncio mode" in args[0] and "data" in kwargs:
            reasoning_logged = True
            print(f"    ✓ Asyncio mode reasoning logged: {args[0]}")
            break
    
    assert reasoning_logged, "Asyncio mode should log reasoning content"
    
    print("✓ Asyncio mode integration works correctly")


def test_temporal_vs_asyncio_consistency():
    """Test that both modes produce consistent reasoning logging."""
    print("Testing temporal vs asyncio consistency...")
    
    # Create test response
    response = create_mock_response_with_thinking_tags()
    
    # Test temporal mode
    mock_logger_temporal = Mock()
    VLLMCompletionTasks._extract_and_log_reasoning_content(response, mock_logger_temporal)
    
    # Test asyncio mode
    mock_llm = Mock()
    mock_llm.logger = Mock()
    VLLMAugmentedLLM._extract_and_log_reasoning_content_asyncio(mock_llm, response)
    
    # Both should have logged reasoning content
    assert mock_logger_temporal.info.called, "Temporal mode should log reasoning"
    assert mock_llm.logger.info.called, "Asyncio mode should log reasoning"
    
    print("✓ Both temporal and asyncio modes log reasoning content")


def main():
    """Run all tests."""
    print("Running Qwen reasoning content logging fix tests...\n")
    
    try:
        test_reasoning_extraction_methods()
        test_asyncio_mode_integration()
        test_temporal_vs_asyncio_consistency()
        
        print("\n🎉 All tests passed! Reasoning content logging fix is working correctly.")
        print("\nKey improvements verified:")
        print("1. ✓ Reasoning content extraction works for both reasoning_content field and thinking tags")
        print("2. ✓ Asyncio mode now has reasoning content logging")
        print("3. ✓ Temporal mode retains enhanced reasoning content logging")
        print("4. ✓ Both modes provide consistent reasoning capture")
        print("5. ✓ Direct execution method added for asyncio mode")
        
        print("\nThe reasoning content should now appear in logs for:")
        print("- Basic example (asyncio): Uses _extract_and_log_reasoning_content_asyncio")
        print("- Temporal example: Uses VLLMCompletionTasks with enhanced logging")
        print("- Both modes: Extract from reasoning_content field and thinking tags")
        
        print("\n" + "="*60)
        print("GIT MERGE INSTRUCTIONS:")
        print("="*60)
        print("To merge this commit into feature/vllm-support branch:")
        print("1. git add . && git commit -m 'Add Qwen reasoning content logging fix'")
        print("2. git checkout feature/vllm-support")
        print("3. git merge <your-current-branch-name>")
        print("   OR if you're on main/master:")
        print("   git merge main")
        print("4. git push origin feature/vllm-support")
        print("\nAlternatively, if you want to merge a specific commit:")
        print("1. git log --oneline (find your commit hash)")
        print("2. git checkout feature/vllm-support") 
        print("3. git cherry-pick <commit-hash>")
        print("4. git push origin feature/vllm-support")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
