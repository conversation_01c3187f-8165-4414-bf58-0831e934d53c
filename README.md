# MCP Agent Log Formatter

This tool helps format MCP Agent J<PERSON> log files for better readability. It parses the JSONL logs and displays them in a more structured and visually appealing format.

## Features

- Colorized output based on log level (DEBUG, INFO, WARNING, ERROR)
- Structured display of log entries in panels
- Filtering by log level
- Filtering by namespace
- Formatted timestamps for better readability
- Pretty-printed JSON data

## Requirements

- Python 3.6+
- Rich library (`pip install rich`)

## Usage

### Basic Usage

```bash
./format_mcp_log.sh <log_file_path>
```

Example:
```bash
./format_mcp_log.sh mcp-agent/examples/basic/mcp_basic_agent/logs/mcp-agent-20250522_143423.jsonl
```

### Filtering by Log Level

To show only logs at or above a specific level:

```bash
./format_mcp_log.sh <log_file_path> --level <LEVEL>
```

Where `<LEVEL>` can be DEBUG, INFO, WARNING, ERROR, or CRIT<PERSON><PERSON>.

Example:
```bash
./format_mcp_log.sh mcp-agent/examples/basic/mcp_basic_agent/logs/mcp-agent-20250522_143423.jsonl --level INFO
```

### Filtering by Namespace

To show only logs from a specific namespace:

```bash
./format_mcp_log.sh <log_file_path> --filter <namespace>
```

Example:
```bash
./format_mcp_log.sh mcp-agent/examples/basic/mcp_basic_agent/logs/mcp-agent-20250522_143423.jsonl --filter mcp_agent.workflows.llm
```

### Saving Output to a File

```bash
./format_mcp_log.sh <log_file_path> --output <output_file>
```

Example:
```bash
./format_mcp_log.sh mcp-agent/examples/basic/mcp_basic_agent/logs/mcp-agent-20250522_143423.jsonl --output formatted_log.txt
```

### Combining Filters

You can combine multiple filters:

```bash
./format_mcp_log.sh <log_file_path> --level INFO --filter mcp_agent.mcp_basic_agent
```

## Direct Python Usage

If you prefer to use the Python script directly:

```bash
python3 format_log.py <log_file_path> [--level LEVEL] [--filter NAMESPACE] [--output OUTPUT_FILE]
```

## Understanding the Log Format

Each log entry is displayed in a panel with the following information:

- **LEVEL**: The log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- **TIME**: The formatted timestamp
- **NAMESPACE**: The namespace of the log entry
- **MESSAGE**: The log message
- **DATA**: Any additional data included in the log entry (if present)

## Notes

- For very large log files, the output may be quite lengthy. Consider using filters to narrow down the results.
- The tool handles truncated JSON gracefully and will display an error message if it encounters malformed JSON.
