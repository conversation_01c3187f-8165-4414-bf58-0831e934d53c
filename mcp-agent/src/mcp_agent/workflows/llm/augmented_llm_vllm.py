"""
Implementation of AugmentedLLM using vLLM as the backend.
This allows using vLLM's high-performance inference engine with MCP's agent framework.
"""

from typing import Any, List, Type

from openai import OpenAI
from openai.types.chat import Chat<PERSON>ompletion, ChatCompletionMessage, ChatCompletionMessageParam
from pydantic import BaseModel

from mcp_agent.config import VLLMSettings
from mcp_agent.executor.workflow_task import workflow_task
from mcp_agent.tracing.telemetry import telemetry
from mcp_agent.utils.common import ensure_serializable
from mcp_agent.utils.pydantic_type_serializer import serialize_model, deserialize_model
from mcp_agent.workflows.llm.augmented_llm import (
    ModelT,
    RequestParams,
)
from mcp_agent.workflows.llm.augmented_llm_openai import (
    OpenAIAugmentedLLM,
)
from mcp_agent.logging.logger import get_logger


class VLLMRequestCompletionRequest(BaseModel):
    config: VLLMSettings
    payload: dict


class VLLMRequestStructuredCompletionRequest(BaseModel):
    config: VLLMSettings
    response_model: Any | None = None
    serialized_response_model: str | None = None
    response_str: str
    model: str


class VLLMAugmentedLLM(OpenAIAugmentedLLM):
    """
    The basic building block of agentic systems is an LLM enhanced with augmentations
    such as retrieval, tools, and memory provided from a collection of MCP servers.
    This implementation uses vLLM's OpenAI-compatible API as the LLM backend.

    Features:
    - Supports all models available in vLLM
    - Implements the OpenAI-compatible API for easy integration
    - Supports tool calling for agent functionality
    - Supports Qwen's "Thinking Mode" feature for enhanced reasoning

    Qwen Thinking Mode:
    Qwen models support a special "Thinking Mode" feature that allows the model to show its
    reasoning process before providing the final answer. This can be enabled or disabled
    using the `enable_thinking` parameter in the generate methods. When enabled, the model
    will first think through the problem step by step (visible in the response) before
    providing its final answer. This can lead to more accurate and well-reasoned responses,
    especially for complex questions.

    Implementation Note:
    The `enable_thinking` parameter is passed to vLLM through the OpenAI client's `extra_body`
    parameter as `{"chat_template_kwargs": {"enable_thinking": true/false}}`. This is a
    vLLM-specific extension to the OpenAI API and is not part of the standard OpenAI API.

    Example:
    ```python
    # Enable thinking mode (default)
    result = await llm.generate_str(
        message="What is the square root of 144?",
        enable_thinking=True
    )

    # Disable thinking mode
    result = await llm.generate_str(
        message="What is the square root of 144?",
        enable_thinking=False
    )
    ```
    """

    def __init__(self, *args, **kwargs):
        # Create a copy of kwargs to avoid modifying the original
        updated_kwargs = kwargs.copy()

        # Set default model if not provided
        if "default_model" not in updated_kwargs:
            updated_kwargs["default_model"] = "Qwen/Qwen3-8B"

        # Initialize with OpenAI-compatible interface
        super().__init__(*args, **updated_kwargs)

        # Set provider name
        self.provider = "vLLM"

        # Initialize logger with name if available
        self.logger = get_logger(f"{__name__}.{self.name}" if self.name else __name__)

        # Get vLLM settings from config
        self.vllm_api_base = "http://0.0.0.0:28701/v1"
        self.vllm_api_key = "EMPTY"

        if self.context and self.context.config and self.context.config.vllm:
            if self.context.config.vllm.api_base:
                self.vllm_api_base = self.context.config.vllm.api_base
            if self.context.config.vllm.api_key:
                self.vllm_api_key = self.context.config.vllm.api_key

        self.logger.info(f"Using vLLM API at {self.vllm_api_base}")

    async def generate(
        self,
        message: str | ChatCompletionMessageParam | List[ChatCompletionMessageParam],
        request_params: RequestParams | None = None,
        enable_thinking: bool = True,
    ) -> List[ChatCompletionMessage]:
        """
        Process a query using an LLM and available tools.
        This implementation uses vLLM's OpenAI-compatible API as the LLM backend.

        Args:
            message: The message to send to the LLM. Can be a string, a single message, or a list of messages.
            request_params: Optional parameters to configure the request.
            enable_thinking: Whether to enable Qwen's "Thinking Mode" feature. This is only applicable for Qwen models
                            and allows the model to show its reasoning process before providing the final answer.
                            Default is True. Set to False to disable thinking mode.

        Returns:
            A list of ChatCompletionMessage objects representing the LLM's responses.
        """
        messages: List[ChatCompletionMessageParam] = []
        params = self.get_request_params(request_params)

        if params.use_history:
            messages.extend(self.history.get())

        system_prompt = self.instruction or params.systemPrompt
        if system_prompt and len(messages) == 0:
            messages.append({
                "role": "system",
                "content": system_prompt
            })

        if isinstance(message, str):
            messages.append({
                "role": "user",
                "content": message
            })
        elif isinstance(message, list):
            messages.extend(message)
        else:
            messages.append(message)

        # Get available tools from the agent
        response = await self.agent.list_tools()
        available_tools = [
            {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.inputSchema,
                },
            }
            for tool in response.tools
        ] if response.tools else None

        responses: List[ChatCompletionMessage] = []
        model = await self.select_model(params)

        for i in range(params.max_iterations):
            # Standard OpenAI API parameters
            arguments = {
                "model": model,
                "messages": messages,
                "max_tokens": params.maxTokens,
                "temperature": params.temperature,
                "stop": params.stopSequences,
            }

            # Add tools if available
            if available_tools:
                arguments["tools"] = available_tools

            # Create extra_body dictionary for vLLM-specific parameters
            extra_body = {
                # Add chat_template_kwargs with enable_thinking parameter for Qwen models
                "chat_template_kwargs": {"enable_thinking": enable_thinking},
            }

            # Add any additional metadata
            if params.metadata:
                arguments = {**arguments, **params.metadata}

            # Add extra_body to arguments
            arguments["extra_body"] = extra_body

            self.logger.debug(f"vLLM request arguments: {arguments}")
            self._log_chat_progress(chat_turn=len(messages) // 2, model=model)

            # Create vLLM configuration from context
            vllm_config = VLLMSettings(
                api_base=self.vllm_api_base,
                api_key=self.vllm_api_key,
            )

            request = VLLMRequestCompletionRequest(
                config=vllm_config,
                payload=arguments,
            )

            # Execute the request through Temporal activity or direct call
            if self.executor and self.executor.execution_engine == "temporal":
                response: ChatCompletion = await self.executor.execute(
                    VLLMCompletionTasks.request_completion_task,
                    ensure_serializable(request),
                )
            else:
                # Direct execution for asyncio mode
                response = await VLLMCompletionTasks._execute_completion_direct(request)

            self.logger.debug(
                "vLLM ChatCompletion response:",
                data=response,
            )

            # Extract and log reasoning content for asyncio mode
            if not (self.executor and self.executor.execution_engine == "temporal"):
                self._extract_and_log_reasoning_content_asyncio(response)

            if isinstance(response, BaseException):
                self.logger.error(f"Error: {response}")
                break

            if not response.choices or len(response.choices) == 0:
                # No response from the model, we're done
                break

            # Get the first choice
            choice = response.choices[0]
            message = choice.message
            responses.append(message)

            # Convert the message to a parameter for the next iteration
            converted_message = self.convert_message_to_message_param(message)
            messages.append(converted_message)

            # Handle tool calls if present
            if choice.finish_reason == "tool_calls" and message.tool_calls:
                # Execute all tool calls in parallel
                tool_tasks = [
                    self.execute_tool_call(tool_call)
                    for tool_call in message.tool_calls
                ]
                # Wait for all tool calls to complete
                tool_results = await self.executor.execute_many(tool_tasks)
                self.logger.debug(
                    f"Iteration {i}: Tool call results: {str(tool_results) if tool_results else 'None'}"
                )
                # Add non-None results to messages
                for result in tool_results:
                    if isinstance(result, BaseException):
                        self.logger.error(
                            f"Warning: Unexpected error during tool execution: {result}. Continuing..."
                        )
                        continue
                    if result is not None:
                        messages.append(result)
            elif choice.finish_reason == "reasoning":
                # This finish reason is returned when using Qwen's thinking mode
                # The model has completed its reasoning process but may continue generating
                self.logger.debug(
                    f"Iteration {i}: Model completed reasoning with finish_reason '{choice.finish_reason}'"
                )
                # We don't break here as we want to continue the conversation
            elif choice.finish_reason in ["length", "content_filter", "stop"]:
                self.logger.debug(
                    f"Iteration {i}: Stopping because finish_reason is '{choice.finish_reason}'"
                )
                break
            else:
                # Handle any other finish reasons that might be returned
                self.logger.debug(
                    f"Iteration {i}: Received unknown finish_reason '{choice.finish_reason}'"
                )
                # We don't break here to allow for future extensions

        if params.use_history:
            self.history.set(messages)

        self._log_chat_finished(model=model)

        return responses

    async def generate_str(
        self,
        message,
        request_params: RequestParams | None = None,
        enable_thinking: bool = True,
    ) -> str:
        """
        Process a query using an LLM and available tools.
        This implementation uses vLLM's OpenAI-compatible API as the LLM backend.

        Args:
            message: The message to send to the LLM. Can be a string, a single message, or a list of messages.
            request_params: Optional parameters to configure the request.
            enable_thinking: Whether to enable Qwen's "Thinking Mode" feature. This is only applicable for Qwen models
                            and allows the model to show its reasoning process before providing the final answer.
                            Default is True. Set to False to disable thinking mode.

        Returns:
            The generated text as a string.
        """
        responses = await self.generate(
            message=message,
            request_params=request_params,
            enable_thinking=enable_thinking,
        )

        final_text: List[str] = []

        for response in responses:
            content = response.content
            if not content:
                continue

            if isinstance(content, str):
                final_text.append(content)
                continue

        return "\n".join(final_text)

    async def generate_structured(
        self,
        message,
        response_model: Type[ModelT],
        request_params: RequestParams | None = None,
        enable_thinking: bool = True,
    ) -> ModelT:
        """
        Generate a structured response using the instructor library.
        This implementation uses vLLM's OpenAI-compatible API as the LLM backend.

        Args:
            message: The message to send to the LLM. Can be a string, a single message, or a list of messages.
            response_model: The Pydantic model to use for parsing the response.
            request_params: Optional parameters to configure the request.
            enable_thinking: Whether to enable Qwen's "Thinking Mode" feature. This is only applicable for Qwen models
                            and allows the model to show its reasoning process before providing the final answer.
                            Default is True. Set to False to disable thinking mode.

        Returns:
            A structured response as a Pydantic model.
        """
        # First we invoke the LLM to generate a string response
        # We need to do this in a two-step process because Instructor doesn't
        # know how to invoke MCP tools via call_tool, so we'll handle all the
        # processing first and then pass the final response through Instructor
        response = await self.generate_str(
            message=message,
            request_params=request_params,
            enable_thinking=enable_thinking,
        )

        params = self.get_request_params(request_params)
        model = await self.select_model(params)

        # Create vLLM configuration from context
        vllm_config = VLLMSettings(
            api_base=self.vllm_api_base,
            api_key=self.vllm_api_key,
        )

        serialized_response_model: str | None = None

        if self.executor and self.executor.execution_engine == "temporal":
            # Serialize the response model to a string
            serialized_response_model = serialize_model(response_model)

        structured_response = await self.executor.execute(
            VLLMCompletionTasks.request_structured_completion_task,
            VLLMRequestStructuredCompletionRequest(
                config=vllm_config,
                response_model=response_model
                if not serialized_response_model
                else None,
                serialized_response_model=serialized_response_model,
                response_str=response,
                model=model,
            ),
        )
        # Convert dict back to the proper model instance if needed
        if isinstance(structured_response, dict):
            structured_response = response_model.model_validate(structured_response)

        return structured_response

    def _extract_and_log_reasoning_content_asyncio(self, response: ChatCompletion) -> None:
        """
        Extract and log reasoning content from vLLM response in asyncio mode.
        This provides the same reasoning content logging as Temporal mode.
        """
        if not response.choices:
            return

        for i, choice in enumerate(response.choices):
            message = choice.message

            # Check for reasoning_content field (vLLM extension)
            if hasattr(message, 'reasoning_content') and message.reasoning_content:
                self.logger.info(
                    f"Qwen Reasoning Content (choice {i}, asyncio mode):",
                    data={"reasoning": message.reasoning_content}
                )

            # Check for thinking tags in content
            elif message.content and isinstance(message.content, str):
                content = message.content

                # Look for common thinking patterns
                thinking_patterns = [
                    ("<think>", "</think>"),
                    ("<thinking>", "</thinking>"),
                    ("**Thinking:**", "**Answer:**"),
                    ("**思考过程:**", "**答案:**"),  # Chinese patterns
                ]

                for start_tag, end_tag in thinking_patterns:
                    if start_tag in content and end_tag in content:
                        start_idx = content.find(start_tag)
                        end_idx = content.find(end_tag, start_idx)
                        if start_idx != -1 and end_idx != -1:
                            reasoning_content = content[start_idx + len(start_tag):end_idx].strip()
                            self.logger.info(
                                f"Qwen Reasoning Content (choice {i}, asyncio mode, extracted from content):",
                                data={"reasoning": reasoning_content}
                            )
                            break

            # Log finish_reason if it indicates reasoning completion
            if choice.finish_reason == "reasoning":
                self.logger.info(
                    f"Qwen model completed reasoning phase (choice {i}, asyncio mode)",
                    data={"finish_reason": choice.finish_reason}
                )


class VLLMCompletionTasks:
    @staticmethod
    @workflow_task
    @telemetry.traced()
    async def request_completion_task(
        request: VLLMRequestCompletionRequest,
    ) -> ChatCompletion:
        """
        Request a completion from vLLM's API.
        """
        from mcp_agent.logging.logger import get_logger

        logger = get_logger(__name__)

        vllm_client = OpenAI(
            api_key=request.config.api_key or "EMPTY",
            base_url=request.config.api_base,
        )

        payload = request.payload
        response = vllm_client.chat.completions.create(**payload)

        # Log the raw response for debugging
        logger.debug("Raw vLLM API response:", data=response)

        # Extract and log reasoning content if present
        VLLMCompletionTasks._extract_and_log_reasoning_content(response, logger)

        # Add reasoning content to telemetry span if present
        VLLMCompletionTasks._add_reasoning_to_telemetry(response)

        response = ensure_serializable(response)
        return response

    @staticmethod
    async def _execute_completion_direct(request: VLLMRequestCompletionRequest) -> ChatCompletion:
        """
        Execute completion directly for asyncio mode (non-Temporal).
        This provides the same functionality as the Temporal activity but without workflow decoration.
        """
        from mcp_agent.logging.logger import get_logger

        logger = get_logger(__name__)

        vllm_client = OpenAI(
            api_key=request.config.api_key or "EMPTY",
            base_url=request.config.api_base,
        )

        payload = request.payload
        response = vllm_client.chat.completions.create(**payload)

        # Log the raw response for debugging
        logger.debug("Raw vLLM API response (asyncio mode):", data=response)

        # Extract and log reasoning content if present
        VLLMCompletionTasks._extract_and_log_reasoning_content(response, logger)

        # Add reasoning content to telemetry span if present
        VLLMCompletionTasks._add_reasoning_to_telemetry(response)

        response = ensure_serializable(response)
        return response

    @staticmethod
    @workflow_task
    @telemetry.traced()
    async def request_structured_completion_task(
        request: VLLMRequestStructuredCompletionRequest,
    ) -> ModelT:
        """
        Request a structured completion using Instructor's vLLM API.
        """
        import instructor
        from instructor.exceptions import InstructorRetryException

        if request.response_model:
            response_model = request.response_model
        elif request.serialized_response_model:
            response_model = deserialize_model(request.serialized_response_model)
        else:
            raise ValueError(
                "Either response_model or serialized_response_model must be provided for structured completion."
            )

        client = instructor.from_openai(
            OpenAI(
                api_key=request.config.api_key or "EMPTY",
                base_url=request.config.api_base,
            ),
        )

        try:
            # Extract structured data from natural language
            structured_response = await client.chat.completions.create(
                model=request.model,
                response_model=response_model,
                messages=[
                    {"role": "user", "content": request.response_str},
                ],
                extra_body={
                    "chat_template_kwargs": {"enable_thinking": True},
                },
            )
        except InstructorRetryException:
            # Retry the request with JSON mode
            client = instructor.from_openai(
                OpenAI(
                    api_key=request.config.api_key or "EMPTY",
                    base_url=request.config.api_base,
                ),
                mode=instructor.Mode.JSON,
            )

            structured_response = await client.chat.completions.create(
                model=request.model,
                response_model=response_model,
                messages=[
                    {"role": "user", "content": request.response_str},
                ],
                extra_body={
                    "chat_template_kwargs": {"enable_thinking": True},
                },
            )

        return structured_response

    @staticmethod
    def _extract_and_log_reasoning_content(response: ChatCompletion, logger) -> None:
        """
        Extract and log reasoning content from vLLM response when thinking mode is enabled.

        vLLM may include reasoning content in various ways:
        1. In the message content itself (prefixed with thinking tags)
        2. In custom response fields (reasoning_content)
        3. In the raw response object
        """
        if not response.choices:
            return

        for i, choice in enumerate(response.choices):
            message = choice.message

            # Check if there's reasoning content in the message
            reasoning_content = None

            # Method 1: Check for reasoning_content field (if vLLM adds it)
            if hasattr(message, 'reasoning_content') and message.reasoning_content:
                reasoning_content = message.reasoning_content
                logger.info(f"Qwen Reasoning Content (choice {i}):", data={"reasoning": reasoning_content})

            # Method 2: Check for thinking tags in content
            elif message.content and isinstance(message.content, str):
                content = message.content

                # Look for common thinking patterns
                thinking_patterns = [
                    ("<think>", "</think>"),
                    ("<thinking>", "</thinking>"),
                    ("**Thinking:**", "**Answer:**"),
                    ("**思考过程:**", "**答案:**"),  # Chinese patterns
                ]

                for start_tag, end_tag in thinking_patterns:
                    if start_tag in content and end_tag in content:
                        start_idx = content.find(start_tag)
                        end_idx = content.find(end_tag, start_idx)
                        if start_idx != -1 and end_idx != -1:
                            reasoning_content = content[start_idx + len(start_tag):end_idx].strip()
                            logger.info(f"Qwen Reasoning Content (choice {i}, extracted from content):",
                                      data={"reasoning": reasoning_content})
                            break

            # Method 3: Check raw response for any custom fields
            if hasattr(response, 'model_extra') and response.model_extra:
                for key, value in response.model_extra.items():
                    if 'reasoning' in key.lower() or 'thinking' in key.lower():
                        logger.info(f"Qwen Reasoning Content (choice {i}, from {key}):",
                                  data={"reasoning": value})

            # Log finish_reason if it indicates reasoning completion
            if choice.finish_reason == "reasoning":
                logger.info(f"Qwen model completed reasoning phase (choice {i})",
                          data={"finish_reason": choice.finish_reason})

    @staticmethod
    def _add_reasoning_to_telemetry(response: ChatCompletion) -> None:
        """
        Add reasoning content to the current telemetry span for observability.
        """
        try:
            from opentelemetry import trace

            current_span = trace.get_current_span()
            if current_span and current_span.is_recording():

                for i, choice in enumerate(response.choices):
                    message = choice.message

                    # Add reasoning-related attributes to span
                    if choice.finish_reason == "reasoning":
                        current_span.set_attribute(f"vllm.choice.{i}.finish_reason", "reasoning")
                        current_span.set_attribute(f"vllm.choice.{i}.has_reasoning", True)

                    # Check for reasoning content in message
                    if message.content and isinstance(message.content, str):
                        content = message.content

                        # Look for thinking patterns
                        thinking_patterns = [
                            ("<think>", "</think>"),
                            ("<thinking>", "</thinking>"),
                            ("**Thinking:**", "**Answer:**"),
                        ]

                        for start_tag, end_tag in thinking_patterns:
                            if start_tag in content and end_tag in content:
                                current_span.set_attribute(f"vllm.choice.{i}.has_thinking_tags", True)
                                current_span.set_attribute(f"vllm.choice.{i}.thinking_pattern", start_tag)

                                # Optionally add reasoning content length (not the content itself for privacy)
                                start_idx = content.find(start_tag)
                                end_idx = content.find(end_tag, start_idx)
                                if start_idx != -1 and end_idx != -1:
                                    reasoning_length = end_idx - start_idx - len(start_tag)
                                    current_span.set_attribute(f"vllm.choice.{i}.reasoning_content_length", reasoning_length)
                                break

                    # Add general thinking mode indicator
                    current_span.set_attribute("vllm.thinking_mode_enabled", True)

        except Exception:
            # Don't let telemetry errors break the main flow
            pass
