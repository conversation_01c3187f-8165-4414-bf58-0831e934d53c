#!/usr/bin/env python3
import argparse
import json
import os
import sys
from datetime import datetime
from typing import Dict, Any, List

# ANSI color codes for terminal output
COLORS = {
    "INFO": "\033[32m",  # Green
    "DEBUG": "\033[36m", # <PERSON>an
    "WARNING": "\033[33m", # Yellow
    "ERROR": "\033[31m", # Red
    "CRITICAL": "\033[31;1m", # Bold Red
    "RESET": "\033[0m"
}

def parse_log_entry(line: str) -> Dict[str, Any]:
    """Parse a single JSONL log entry."""
    try:
        return json.loads(line)
    except json.JSONDecodeError:
        return {"level": "ERROR", "timestamp": datetime.now().isoformat(), 
                "message": f"Failed to parse log line: {line}"}

def format_timestamp(timestamp: str) -> str:
    """Format an ISO timestamp to a more readable format."""
    try:
        dt = datetime.fromisoformat(timestamp)
        return dt.strftime("%H:%M:%S.%f")[:-3]
    except (ValueError, TypeError):
        return timestamp

def get_data_string(data: Dict[str, Any], indent_level: int = 1) -> str:
    """Format the data dictionary nicely with indentation."""
    if not data:
        return ""
    
    formatted = []
    indent = "  " * indent_level
    
    if "data" in data and isinstance(data["data"], dict):
        data = data["data"]
    
    for key, value in data.items():
        if isinstance(value, dict):
            sub_data = get_data_string(value, indent_level + 1)
            formatted.append(f"{indent}{key}:")
            formatted.append(sub_data)
        elif isinstance(value, list):
            formatted.append(f"{indent}{key}:")
            for item in value:
                if isinstance(item, dict):
                    sub_data = get_data_string(item, indent_level + 2)
                    formatted.append(f"{indent}  - {sub_data.lstrip()}")
                else:
                    formatted.append(f"{indent}  - {item}")
        else:
            formatted.append(f"{indent}{key}: {value}")
    
    return "\n".join(formatted)

def format_log_entry(entry: Dict[str, Any], color: bool = True) -> str:
    """Format a log entry for display."""
    level = entry.get("level", "UNKNOWN")
    timestamp = format_timestamp(entry.get("timestamp", ""))
    namespace = entry.get("namespace", "")
    message = entry.get("message", "")
    
    level_color = COLORS.get(level, "") if color else ""
    reset = COLORS["RESET"] if color else ""
    
    header = f"{timestamp} {level_color}[{level}]{reset} {namespace}: {message}"
    
    data_output = ""
    if "data" in entry:
        data_output = "\n" + get_data_string(entry["data"])
    
    return f"{header}{data_output}"

def filter_entries(entries: List[Dict[str, Any]], level: str, namespace: str, keyword: str) -> List[Dict[str, Any]]:
    """Filter log entries based on criteria."""
    filtered = entries
    
    if level:
        filtered = [e for e in filtered if e.get("level", "") == level]
    
    if namespace:
        filtered = [e for e in filtered if namespace in e.get("namespace", "")]
    
    if keyword:
        filtered = [e for e in filtered if 
                  keyword in e.get("message", "") or 
                  (e.get("data") and keyword in json.dumps(e.get("data")))]
    
    return filtered

def main():
    parser = argparse.ArgumentParser(description="Format JSONL logs for better readability.")
    parser.add_argument("input", nargs="?", help="Input log file (JSONL format). Use '-' or omit for stdin.")
    parser.add_argument("-o", "--output", help="Output file. If not specified, prints to stdout.")
    parser.add_argument("-l", "--level", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], 
                        help="Filter by log level.")
    parser.add_argument("-n", "--namespace", help="Filter by namespace (substring match).")
    parser.add_argument("-k", "--keyword", help="Filter by keyword in message or data.")
    parser.add_argument("--no-color", action="store_true", help="Disable colored output.")
    
    args = parser.parse_args()
    
    # Set up input
    if not args.input or args.input == "-":
        input_stream = sys.stdin
    else:
        try:
            input_stream = open(args.input, "r")
        except OSError as e:
            print(f"Error opening input file: {e}", file=sys.stderr)
            return 1
    
    # Parse all entries
    entries = []
    for line in input_stream:
        line = line.strip()
        if line:
            entries.append(parse_log_entry(line))
    
    if input_stream != sys.stdin:
        input_stream.close()
    
    # Apply filters
    filtered_entries = filter_entries(entries, args.level, args.namespace, args.keyword)
    
    # Format and output
    output_lines = [format_log_entry(entry, not args.no_color) for entry in filtered_entries]
    output_text = "\n\n".join(output_lines)
    
    if args.output:
        try:
            with open(args.output, "w") as f:
                f.write(output_text)
        except OSError as e:
            print(f"Error writing to output file: {e}", file=sys.stderr)
            return 1
    else:
        print(output_text)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
