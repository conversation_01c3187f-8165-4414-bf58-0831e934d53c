$schema: ../../../schema/mcp-agent.config.schema.json

execution_engine: asyncio
logger:
  type: console
  level: debug
  batch_size: 100
  flush_interval: 2
  max_queue_size: 2048
  http_endpoint:
  http_headers:
  http_timeout: 5

mcp:
  servers:
    fetch:
      command: "uvx"
      args: ["mcp-server-fetch"]
    filesystem:
      command: "npx"
      args: ["-y", "@modelcontextprotocol/server-filesystem"]

openai:
  base_url: "http://localhost:11434/v1"
  api_key: ollama
