import asyncio
import os
import time

from mcp_agent.app import <PERSON><PERSON><PERSON>
from mcp_agent.config import (
    VLLMSettings,
    Settings,
    LoggerSettings,
    MCPSettings,
    MCPServerSettings,
)
from mcp_agent.agents.agent import Agent
from mcp_agent.workflows.llm.augmented_llm import RequestParams
from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLLM

"""
This example demonstrates how to use Qwen's "Thinking Mode" feature with VLLMAugmentedLLM.

Qwen models support a special "Thinking Mode" feature that allows the model to show its
reasoning process before providing the final answer. This can be enabled or disabled
using the `enable_thinking` parameter in the generate methods.

Note: This example requires a running vLLM server with a Qwen model loaded.
You can start the server with:

vllm serve Qwen/Qwen3-8B --host localhost --port 8000

Make sure to update the api_base and default_model in the settings if needed.
"""

settings = Settings(
    execution_engine="asyncio",
    logger=LoggerSettings(type="file", level="debug"),
    mcp=MCPSettings(
        servers={
            "fetch": MCPServerSettings(
                command="uvx",
                args=["mcp-server-fetch"],
            ),
        }
    ),
    vllm=VLLMSettings(
        api_base="http://0.0.0.0:28701/v1",
        # Use a Qwen model to demonstrate thinking mode
        default_model="Qwen/Qwen3-8B",
        api_key="EMPTY",  # vLLM doesn't require an API key by default
    ),
)

# Settings can either be specified programmatically,
# or loaded from mcp_agent.config.yaml/mcp_agent.secrets.yaml
app = MCPApp(
    name="mcp_basic_vllm_agent"
    # settings=settings
)

async def example_usage():
    async with app.run() as agent_app:
        logger = agent_app.logger
        context = agent_app.context

        logger.info("Current config:", data=context.config.model_dump())

        # Add the current directory to the filesystem server's args
        context.config.mcp.servers["filesystem"].args.extend([os.getcwd()])

        finder_agent = Agent(
            name="finder",
            instruction="""You are an agent with access to the filesystem,
            as well as the ability to fetch URLs. Your job is to identify
            the closest match to a user's request, make the appropriate tool calls,
            and return the URI and CONTENTS of the closest match.""",
            server_names=["fetch", "filesystem"],
        )

        async with finder_agent:
            logger.info("finder: Connected to server, calling list_tools...")
            result = await finder_agent.list_tools()
            logger.info("Tools available:", data=result.model_dump())

            llm = await finder_agent.attach_llm(VLLMAugmentedLLM)
            result = await llm.generate_str(
                message="Print the contents of mcp_agent.config.yaml verbatim",
            )
            logger.info(f"mcp_agent.config.yaml contents: {result}")

            # Let's switch the same agent to a different LLM
            # llm = await finder_agent.attach_llm(AnthropicAugmentedLLM)

            result = await llm.generate_str(
                message="Print the first 2 paragraphs of https://modelcontextprotocol.io/introduction",
            )
            logger.info(f"First 2 paragraphs of Model Context Protocol docs: {result}")

            # Multi-turn conversations
            result = await llm.generate_str(
                message="Summarize those paragraphs in a 128 character tweet",
                # You can configure advanced options by setting the request_params object
                request_params=RequestParams(
                    # You can set the model directly using the 'model' field
                    model="Qwen/Qwen3-8B",
                    # Set temperature for more deterministic results
                    temperature=0.2,
                ),
            )
            logger.info(f"Paragraph as a tweet: {result}")


# async def example_usage():
#     async with app.run() as agent_app:
#         logger = agent_app.logger
#         context = agent_app.context

#         logger.info("Current config:", data=context.config.model_dump())

#         agent = Agent(
#             name="math-solver",
#             instruction="""You are a helpful math assistant that can solve complex math problems.
#             You should think step by step to arrive at the correct answer.""",
#             server_names=["fetch"],
#         )

#         async with agent:
#             logger.info("math-solver: Connected to server, calling list_tools...")
#             result = await agent.list_tools()
#             logger.info("Tools available:", data=result.model_dump())

#             # Create the VLLMAugmentedLLM instance
#             llm = await agent.attach_llm(VLLMAugmentedLLM)

#             # Example 1: With thinking mode enabled (default)
#             logger.info("Example 1: With thinking mode enabled (default)")
#             result = await llm.generate_str(
#                 message="What is the square root of 144? Please solve step by step.",
#                 enable_thinking=True,
#             )
#             logger.info(f"Result with thinking mode enabled:\n{result}")

#             # Example 2: With thinking mode disabled
#             logger.info("\nExample 2: With thinking mode disabled")
#             result = await llm.generate_str(
#                 message="What is the square root of 144? Please solve step by step.",
#                 enable_thinking=False,
#             )
#             logger.info(f"Result with thinking mode disabled:\n{result}")

#             # Example 3: Complex problem with thinking mode
#             logger.info("\nExample 3: Complex problem with thinking mode")
#             result = await llm.generate_str(
#                 message="""Solve this step by step:
#                 If a train travels at 60 miles per hour, how long will it take to travel 150 miles?
#                 """,
#                 enable_thinking=True,
#             )
#             logger.info(f"Result for complex problem with thinking mode:\n{result}")


if __name__ == "__main__":
    start = time.time()
    asyncio.run(example_usage())
    end = time.time()
    t = end - start

    print(f"Total run time: {t:.2f}s")
