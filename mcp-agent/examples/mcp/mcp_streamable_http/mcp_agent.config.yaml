$schema: ../../../schema/mcp-agent.config.schema.json

execution_engine: asyncio
logger:
  transports: [console, file]
  level: debug
  show_progress: true
  path_settings:
    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"
    unique_id: "timestamp" # Options: "timestamp" or "session_id"
    timestamp_format: "%Y%m%d_%H%M%S"

mcp:
  servers:
    stateless_http:
      description: "A streamable HTTP server that is stateless."
      transport: streamable_http
      url: http://0.0.0.0:3156/mcp
      headers:
        my-header: "some_value"

openai:
  # Secrets (API keys, etc.) are stored in an mcp_agent.secrets.yaml file which can be gitignored
  #  default_model: "o3-mini"
  default_model: "gpt-4o-mini"
