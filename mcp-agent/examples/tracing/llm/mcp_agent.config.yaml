$schema: ../../../schema/mcp-agent.config.schema.json

execution_engine: asyncio
logger:
  transports: [file]
  level: debug
  progress_display: true
  path_settings:
    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"
    unique_id: "timestamp" # Options: "timestamp" or "session_id"
    timestamp_format: "%Y%m%d_%H%M%S"

mcp:
  servers:
    fetch:
      command: "uvx"
      args: ["mcp-server-fetch"]
    filesystem:
      command: "npx"
      args: ["-y", "@modelcontextprotocol/server-filesystem"]

openai:
  # Secrets (API keys, etc.) are stored in an mcp_agent.secrets.yaml file which can be gitignored
  #  default_model: "o3-mini"
  default_model: "gpt-4o-mini"

otel:
  enabled: true
  exporters: ["console", "file", "otlp"]
  # OTLP settings for sending traces to collectors like <PERSON><PERSON><PERSON>
  otlp_settings:
    endpoint: "http://10.101.8.102:4318/v1/traces"
  service_name: "BasicTracingLLMExample"
  service_instance_id: "instance-1"  # Optional: unique identifier for this service instance
  service_version: "1.0.0"           # Optional: version of your service
  sample_rate: 1.0                   # Optional: 1.0 = sample all traces, 0.5 = sample 50%
  # Optional: Custom path settings for file exporter
  path_settings:
    path_pattern: "traces/mcp-agent-trace-{unique_id}.jsonl"
    unique_id: "timestamp"           # Options: "timestamp" or "session_id"
    timestamp_format: "%Y%m%d_%H%M%S"
