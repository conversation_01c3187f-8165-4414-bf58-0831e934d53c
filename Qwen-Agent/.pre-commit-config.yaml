repos:
  - repo: https://github.com/pycqa/flake8.git
    rev: 5.0.4
    hooks:
      - id: flake8
        args: ["--max-line-length=300"]  # TODO: Set to 120 and `pre-commit run --all-files`.
  - repo: https://github.com/PyCQA/isort.git
    rev: 5.11.5
    hooks:
      - id: isort
        args: ["--line-length", "120"]
  - repo: https://github.com/pre-commit/mirrors-yapf.git
    rev: v0.32.0
    hooks:
      - id: yapf
        args: ["--style", "{based_on_style: google, column_limit: 120}", "-i"]
  - repo: https://github.com/pre-commit/pre-commit-hooks.git
    rev: v4.3.0
    hooks:
      - id: trailing-whitespace
      - id: check-yaml
      - id: end-of-file-fixer
      - id: requirements-txt-fixer
      - id: double-quote-string-fixer
      - id: check-merge-conflict
      - id: fix-encoding-pragma
        args: ["--remove"]
      - id: mixed-line-ending
        args: ["--fix=lf"]
