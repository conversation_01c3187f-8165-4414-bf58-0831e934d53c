<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON><PERSON>wen</title>
<!--  <script src="popup.js"></script>-->
  <style>
    .title-style {
      margin-top: 10px;
      margin-left: 5px;
      font-family: Arial, sans-serif;
      font-size: 24px;
      color: #333;
    }

    body {
        width: 500px;
        height: 600px;
        background-color: aliceblue;
    }
    .contnet {
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        align-items: center;
    }
    .upload_file {
        display: flex;
        flex-direction: column;
        margin-top: 10px;
    }
    .upload_btn {

        border: none;
        border-radius: 5px;
        background-color: #5c5cdf;
        font-size: 16px;
        color: white;
        font-weight: 400;
        cursor: pointer;
        width: 70px;
        height: 35px;
    }
    .upload_btn:hover {
            border: none;
            border-radius: 10px;
            background-color: #7f7ff2;
            font-size: 16px;
            color: white;
            font-weight: 400;
            cursor: pointer;
            box-shadow: 0px 0px 0px 1px #848181;
        }

    .div-with-copy-paste {
      width: 340px;
      height: 200px;
      background-color: #f2f2f2;
      border: 1px solid #ccc;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
      position: relative;
    }

    .input-text {
      width: 300px;
      /* height: 100px; */
      background-color: #fffdfd;
      border: 1px solid #ccc;
      border-radius: 10px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
      position: left;
      margin-top: 5px;
      margin-bottom: 5px;
    }

    .copy-button {
      /* position: absolute;
      top: 10px;
      right: 10px; */
      background-color: #5085cf;
      color: white;
      padding: 8px 8px;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }

    .copy-button:hover {
      background-color: #45a049;
    }

    ::placeholder {
      color: rgb(197, 196, 196);
    }

    iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
    </style>

</head>


<body>
<!--    <iframe src=$popup_url style="height: 550px"></iframe>-->
<div id="iframe_area" style="height: 570px"></div>

<h3>Customize Address:</h3>
<input type="text" id="addr" name="addr" class="input-text">
<button id="set_addr" class="upload_btn">Change</button>

<script src="popup.js"></script>
</body>
</html>
