from langchain_together import ChatTogether
import os
os.environ["TOGETHER_API_KEY"] = '0016d0feb16b14133e3914cf5176973d8656bff54bbdb8cdf34c4b72feb72959'
llm = ChatTogether(
    model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
    temperature=0,
    max_tokens=None,
    timeout=None,
    max_retries=2,
    # other params...
)

messages = [
    (
        "system",
        "You are a helpful assistant. answer the user's question.",
    ),
    ("human", "introduce TSMC company"),
]
ai_msg = llm.invoke(messages)
print(ai_msg.content)

