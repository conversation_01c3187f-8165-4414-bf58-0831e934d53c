# import getpass
# import os
openai_api_key = "EMPTY"
openai_api_base = "http://192.168.1.54:8701/v1"

# from langchain_community.llms import VLLM

from langchain_community.llms import VLLMOpenAI

llm = VLLMOpenAI(
    openai_api_key="EMPTY",
    openai_api_base="http://192.168.1.54:8701/v1",
    model_name="meta-llama/Meta-Llama-3-8B-Instruct",
)

response = llm.invoke("introduce TSMC company")
print(response)  # e.g., "a city that is filled with history, ancient buildings, and art around every corner"
