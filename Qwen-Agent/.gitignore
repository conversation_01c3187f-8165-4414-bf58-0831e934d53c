env
*.pyc
__pycache__

.idea
.vscode
.DS_Store
*.ipynb_checkpoints

qwen_agent/llm/gpt.py
qwen_agent/llm/tools.py
workspace/*

benchmark/log/*
benchmark/output_data/*
benchmark/upload_file/*
benchmark/upload_file_clean/*
benchmark/eval_data/
Qwen-Agent

docqa/*
log/*
log.jsonl

ai_agent/debug.json
ai_agent/local_prompts/*
**/debug.json
**/debug.log*
debug.json
ai_agent/log.jsonl
qwen_agent.egg-info/*
build/*
dist/*

examples/*.ipynb
**/workspace/*
test/*
tests/env.sh
examples/docqa_multi_agent.py
examples/docqa_multihp_agents.py
**/workspace/*
test/*
tests/env.sh
examples/data/*
test.db
