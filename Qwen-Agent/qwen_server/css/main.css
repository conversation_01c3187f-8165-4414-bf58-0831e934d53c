#warning {background-color: #FFCCCB}
.rec {
    font-size: 16px !important;
    text-align:left}
.title {
    font-size: 30px !important;
    text-align:center}
.desc {
    font-size: 16px !important;
    text-align:center}

.div_tmp {
    height: 190px;
    border-radius: 5px
}

.div_rec {
    height: 300px;
    border-radius: 5px
}

.bt_small_font{
    font-size: 6px;
}

.bt_small{
    width: 30px;
}


.md_tmp {
    height: calc(100dvh - 380px);
    border-radius: 5px

}

.add_scrollbar {
    overflow-y: scroll;
}

.content {
    height: calc(100% - 50px);
    overflow-y: scroll;
  }




.custom-checkbox {
appearance: none;
-webkit-appearance: none;
-moz-appearance: none;
width: 10px;
height: 10px;
border-radius: 50%;
border: 2px solid #ccc;
outline: none;
cursor: pointer;
}

.custom-checkbox:checked {
background-color: rgb(100, 239, 144);
}

.dark svg {
    fill: white;
}

.dark a {
    color: white !important;
}

.textbox_default textarea {
    height: calc(100dvh - 200px);
}

.textbox_default_output textarea {
    height: calc(100dvh - 200px);
}

.textbox textarea {
    height: calc(100dvh - 200px);
}

.textbox_default textarea,
.textbox_default_output textarea,
.textbox textarea
{
    font-size: 16px !important;
    color: #46464A !important;
}

.dark textarea {
    color: #efefef !important;
}

@media screen and (max-width: 711px) {
    .textbox_default textarea {
        height: calc(100dvh - 271px);
    }

    div .default-token-counter {
        top: calc( 0.5 * (100dvh - 245px) ) !important;
    }
}

/* Hide the gradio footer*/
footer {
    display: none !important;
}

button {
    font-size: 14px !important;
}

.token-counter {
  position: absolute !important;
  top: calc( 0.5 * (100dvh - 215px) ) !important;
  right: 2px;
  z-index: 100;
  background: var(--input-background-fill) !important;
  min-height: 0 !important;
}

.default-token-counter {
  top: calc( 0.5 * (100dvh - 255px) ) !important;
}

.token-counter span {
  padding: 1px;
  box-shadow: 0 0 0 0.3em rgba(192,192,192,0.15), inset 0 0 0.6em rgba(192,192,192,0.075);
  border: 2px solid rgba(192,192,192,0.4) !important;
  border-radius: 0.4em;
}
