FROM nvcr.io/nvidia/cuda:12.3.1-devel-ubuntu20.04

ENV DEBIAN_FRONTEND=noninteractive

# Install Python package
RUN apt-get update && apt-get -y --no-install-recommends install \
    software-properties-common && \
    add-apt-repository ppa:deadsnakes/ppa && \
    apt-get update && apt-get -y --no-install-recommends install \
    python3.10 \
    curl && \
    curl -sS https://bootstrap.pypa.io/get-pip.py | python3.10 && \
    rm -rf /var/lib/apt/lists/*

RUN rm /usr/bin/python3 && \
    ln -s /usr/bin/python3.10 /usr/bin/python3

# Install requirements
WORKDIR /qct/genai
COPY requirements /qct/genai/requirements
RUN pip3 install --no-cache-dir -U pip && \
    CMAKE_ARGS="-DGGML_CUDA=on" FORCE_CMAKE=1 pip install --no-cache-dir llama-cpp-python && \
    pip install --no-cache-dir -r requirements/inference.txt && \
    pip install --no-cache-dir -r requirements/api.txt && \
    rm -rf /root/.cache/pip && \
    rm -rf requirements

# Install Unstructured dependencies
RUN apt-get update && apt-get -y --no-install-recommends install \
    poppler-utils \
    tesseract-ocr && \
    rm -rf /var/lib/apt/lists/* && \
    pip install --no-cache-dir pysqlite3-binary && \
    rm -rf /root/.cache/pip && \
    python3 -c "import nltk; nltk.download('punkt_tab'); nltk.download('averaged_perceptron_tagger_eng')"

# libGL.so.1
ENV LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/opt/nvidia/nsight-compute/2023.3.1/host/linux-desktop-glibc_2_11_3-x64/Mesa/

# Whisper
ENV LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/lib/python3.10/dist-packages/nvidia/cudnn/lib

# Install wheels
COPY dist /qct/genai/dist
WORKDIR /qct/genai
RUN pip install dist/*.whl && \
    rm -rf dist

# pre-install
COPY pre_install /qct/genai/pre_install
WORKDIR /qct/genai/pre_install
RUN python3 pre_install.py && \
    cd .. && rm -rf pre_install

WORKDIR /qct/genai
CMD flask --app genai_api run
