FROM nvcr.io/nvidia/cuda:12.3.1-devel-ubuntu20.04

RUN apt-get update -y && apt-get install -y \
    curl \
    git

# Install Anaconda
WORKDIR /tmp
RUN curl -O https://repo.anaconda.com/archive/Anaconda3-2023.09-0-Linux-x86_64.sh && \
    bash Anaconda3-2023.09-0-Linux-x86_64.sh -b -p /opt/conda && \
    rm Anaconda3-2023.09-0-Linux-x86_64.sh

# Copy source code
WORKDIR /qct/genai/src
COPY ./genai-devkit /qct/genai/src/genai-devkit
