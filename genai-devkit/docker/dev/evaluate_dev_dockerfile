FROM nvcr.io/nvidia/cuda:12.3.1-devel-ubuntu20.04
USER root

RUN export DEBIAN_FRONTEND=noninteractive \
    && apt update -y && apt install git wget -y \
    && cd /qct \
    && wget http://***********:5000/qmniai/Miniforge3-Linux-x86_64.sh \
    && bash ./Miniforge3-Linux-x86_64.sh -b \
    && rm -f Miniforge3-Linux-x86_64.sh \
    && source /root/miniforge3/etc/profile.d/conda.sh \
    && yes | conda create --name genai-devkit python=3.10 \
    && conda activate genai-devkit \
    && git clone http://***********/5g-use-case/genai-devkit.git -b feature/train \
    && cd genai-devkit \
    && pip3 install -r /qct/genai-devkit/requirements/evaluate.txt