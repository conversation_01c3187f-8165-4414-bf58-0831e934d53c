#!/bin/bash
set -e

# Reset the log file
printf "" > generate_wheel.log > stdout.log
tail -f stdout.log &
trap "pkill -P $$" EXIT

exec 1>>generate_wheel.log 2>>generate_wheel.log


echo "Start to generate wheel" | tee -a stdout.log

# Create a temporary directory
tmp_dir=source_code
if [ -d "$tmp_dir" ]; then
    rm -rf $tmp_dir
fi
mkdir $tmp_dir

search_dir=../..
for entry in "$search_dir"/*
do
if [ "$entry" == "$search_dir/docker" -o "$entry" == "$search_dir/dist" ]; then
    echo "Skip $entry" | tee -a stdout.log
    continue
elif [ -d "$entry" ]; then
    cp -r $entry ./source_code
else
    cp $entry ./source_code
fi
done

# Build the docker image and run the container to generate wheel file
dockerfile=dockerfile
image_name=qct/genai_build:latest
container_name=genai_build

# Check if the container exists
if [ "$(docker ps -a | grep $container_name)" ]
then
    echo "Container \"$container_name\" already exists, remove it" | tee -a stdout.log
    exit 1
fi

# Check if the image exists
if [ "$(docker images $image_name -q)" ]
then
    echo "Remove existing image: $image_name" | tee -a stdout.log
    docker rmi $image_name
fi

# Build image
echo "Build image: $image_name" | tee -a stdout.log
docker build -t $image_name -f $dockerfile --no-cache .

# Run container
echo "Run container: $container_name" | tee -a stdout.log
docker run -itd --name $container_name $image_name

# Copy wheel file from the container
echo "Copy wheel file from the container" | tee -a stdout.log
docker cp $container_name:/qct/genai/src/genai-devkit/dist .
if [ -d "../../dist" ]; then
    rm -rf ../../dist
fi
mv dist ../../

# Remove container
echo "Remove container and image" | tee -a stdout.log
docker rm -f $container_name
docker rmi $image_name

# Remove temperary directory
rm -rf $tmp_dir

echo "Generate wheel successfully" | tee -a stdout.log
