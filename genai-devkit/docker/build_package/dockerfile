FROM ubuntu:20.04

# Install python package
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3-pip \
    python3.10 && \
    rm -rf /var/lib/apt/lists/*


# Install requirements
WORKDIR /qct/genai
COPY source_code /qct/genai/src/genai-devkit

RUN pip3 install --no-cache-dir --upgrade setuptools pip wheel build && \
    rm -rf requirements

# Build
WORKDIR /qct/genai/src/genai-devkit
RUN python3 -m build
