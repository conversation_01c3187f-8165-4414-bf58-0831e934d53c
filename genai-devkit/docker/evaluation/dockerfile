FROM nvcr.io/nvidia/cuda:12.3.1-devel-ubuntu20.04

ENV DEBIAN_FRONTEND=noninteractive

# Install Python package
RUN apt-get update && apt-get -y --no-install-recommends install \
    software-properties-common && \
    add-apt-repository ppa:deadsnakes/ppa && \
    apt-get update && apt-get -y --no-install-recommends install \
    python3.10 \
    curl && \
    curl -sS https://bootstrap.pypa.io/get-pip.py | python3.10 && \
    rm -rf /var/lib/apt/lists/*

RUN rm /usr/bin/python3 && \
    ln -s /usr/bin/python3.10 /usr/bin/python3

# Install requirements
WORKDIR /qct/genai
COPY requirements /qct/genai/requirements
RUN pip3 install --no-cache-dir -U pip && \
    pip install --no-cache-dir -r requirements/evaluation.txt && \
    pip install --no-cache-dir -r requirements/api.txt && \
    rm -rf /root/.cache/pip && \
    rm -rf requirements

# Install wheels
COPY dist /qct/genai/dist
RUN pip install dist/*.whl && \
    rm -rf dist

WORKDIR /qct/genai
CMD flask --app genai_api run
