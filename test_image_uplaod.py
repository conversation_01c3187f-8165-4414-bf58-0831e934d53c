# from langchain_together import ChatTogether
import os
os.environ["TOGETHER_API_KEY"] = 'b173b4a688f49626002b22c508f412076fc8c386dd2cddcb22c285a8d1aab7c9'
# llm = ChatTogether(
#     model="meta-llama/Llama-Vision-Free",
#     temperature=0,
#     max_tokens=None,
#     timeout=None,
#     max_retries=2,
#     # other params...
# )

# messages = [
#     (
#         "system",
#         "You are a helpful assistant. answer the user's question.",
#     ),
#     ("human", "introduce TSMC company"),
# ]
# ai_msg = llm.invoke(messages)
# print(ai_msg.content)

from together import Together
import base64

client = Together()

getDescriptionPrompt = "compare the two images and tell me the difference."

imagePath= "/merge/test.png"

def encode_image(image_path):
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

base64_image = encode_image(imagePath)

stream = client.chat.completions.create(
    model="meta-llama/Llama-Vision-Free",
    messages=[{
        "role": "user",
        "content": [
            {
                "type": "text",
                "text": "Compare these two images."
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": "https://huggingface.co/datasets/patrickvonplaten/random_img/resolve/main/yosemite.png"
                }
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": "https://huggingface.co/datasets/patrickvonplaten/random_img/resolve/main/slack.png"
                }
            }
        ]
    }]
)
print(stream)
# for chunk in stream:
#     print(chunk.choices[0].delta.content or "" if chunk.choices else "", end="", flush=True)