#!/bin/bash

# Check if the log file path is provided
if [ $# -lt 1 ]; then
    echo "Usage: $0 <log_file_path> [output_file] [--filter namespace] [--level LEVEL]"
    echo "Example: $0 mcp-agent/examples/basic/mcp_basic_agent/logs/mcp-agent-20250522_143423.jsonl"
    exit 1
fi

LOG_FILE=$1
shift

# Install required packages if not already installed
pip install rich > /dev/null 2>&1

# Run the Python script with the provided arguments
python3 format_log.py "$LOG_FILE" "$@"
