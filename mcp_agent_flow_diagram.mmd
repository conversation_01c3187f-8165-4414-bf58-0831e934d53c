sequenceDiagram
    participant User as User Request
    participant App as MCP Application
    participant Logger as Logger System
    participant ConnMgr as Connection Manager
    participant Fetch as Fetch Server
    participant FS as Filesystem Server
    participant Agent as Agent (Finder)
    participant LL<PERSON> as vLLM (Qwen3-8B)
    participant Tools as Tool Execution

    Note over App: 2025-06-02T10:11:04.953748
    App->>Logger: Configure logger with debug level
    
    Note over App: Initialize MCP Application
    App->>App: Register global workflow tasks
    App->>App: Register OpenAI completion tasks
    App->>Logger: Log MCPApp initialization
    App->>Logger: Log current configuration
    
    Note over Agent: Initialize Agent
    App->>Agent: Initialize agent finder
    
    Note over ConnMgr: Setup MCP Connections
    ConnMgr->>Fetch: Find fetch server configuration
    ConnMgr->>FS: Find filesystem server configuration
    ConnMgr->>Fetch: Establish persistent connection
    ConnMgr->>FS: Establish persistent connection
    
    Note over ConnMgr: Initialize MCP Sessions
    ConnMgr->>Fetch: Send initialize request
    ConnMgr->>FS: Send initialize request
    Fetch-->>ConnMgr: Return capabilities & server info
    FS-->>ConnMgr: Return capabilities & server info
    ConnMgr->>Fetch: Send initialized notification
    ConnMgr->>FS: Send initialized notification
    
    Note over ConnMgr: Discover Tools & Prompts
    ConnMgr->>Fetch: Request tools/list
    Fetch-->>ConnMgr: Return fetch tool
    ConnMgr->>Fetch: Request prompts/list
    Fetch-->>ConnMgr: Return fetch prompt
    ConnMgr->>FS: Request tools/list
    FS-->>ConnMgr: Return 11 filesystem tools
    
    Note over Agent: Agent Ready
    Agent->>Logger: Agent finder initialized
    App->>Logger: Connected to servers, list tools
    App->>Logger: Log available tools (12 total)
    
    Note over User: User Request Processing
    User->>Agent: "Print contents of mcp_agent.config.yaml verbatim"
    Agent->>LLM: Prepare vLLM request with tools
    Agent->>Logger: Log vLLM request arguments
    Agent->>Logger: Chat in progress (turn 1)
    
    Note over LLM: LLM Processing with Thinking Mode
    LLM->>LLM: Enable thinking mode reasoning
    LLM->>LLM: Analyze request and available tools
    LLM->>LLM: Decide to use filesystem_read_file
    LLM-->>Agent: Return tool call decision
    
    Note over Tools: Tool Execution
    Agent->>Logger: Log tool call request
    Agent->>FS: Call filesystem_read_file("mcp_agent.config.yaml")
    FS-->>Agent: Return file contents
    Agent->>Logger: Log tool call results
    
    Note over LLM: Final Response Generation
    Agent->>LLM: Send tool results for final response
    Agent->>Logger: Chat in progress (turn 2)
    LLM->>LLM: Process tool results
    LLM-->>Agent: Generate final formatted response
    Agent-->>User: Return verbatim file contents

    Note over App: Session Complete
    rect rgb(240, 248, 255)
        Note over App,User: Key Components:<br/>- MCP Servers: fetch, filesystem<br/>- LLM: Qwen/Qwen3-8B with thinking mode<br/>- Tools: 12 available (1 fetch + 11 filesystem)<br/>- Session ID: e1000c08-a74f-4015-aa54-015e31c7013a
    end
